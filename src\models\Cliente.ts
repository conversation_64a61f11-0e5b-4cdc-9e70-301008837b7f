import db from '../database/db';

export interface Cliente {
  id?: number;
  nome: string;
  data_nascimento: string;
  sexo: 'M' | 'F';
  email?: string;
  telefone?: string;
  data_cadastro: string;
  observacoes?: string;
}

export const ClienteModel = {
  criar: async (cliente: Cliente): Promise<number> => {
    const stmt = db.prepare(`
      INSERT INTO clientes (nome, data_nascimento, sexo, email, telefone, data_cadastro, observacoes)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    const info = await stmt.run(
      cliente.nome,
      cliente.data_nascimento,
      cliente.sexo,
      cliente.email || null,
      cliente.telefone || null,
      cliente.data_cadastro || new Date().toISOString().split('T')[0],
      cliente.observacoes || null
    );

    return info.lastInsertRowid as number;
  },

  obterTodos: async (): Promise<Cliente[]> => {
    const stmt = db.prepare('SELECT * FROM clientes ORDER BY nome');
    const result = await stmt.all();
    return Array.isArray(result) ? result : [];
  },

  obterPorId: async (id: number): Promise<Cliente | undefined> => {
    const stmt = db.prepare('SELECT * FROM clientes WHERE id = ?');
    return await stmt.get(id);
  },
  
  atualizar: async (id: number, cliente: Partial<Cliente>): Promise<boolean> => {
    const campos = Object.keys(cliente).filter(campo => campo !== 'id');
    if (campos.length === 0) return false;

    const setClauses = campos.map(campo => `${campo} = ?`).join(', ');
    const valores = campos.map(campo => (cliente as any)[campo]);

    const stmt = db.prepare(`UPDATE clientes SET ${setClauses} WHERE id = ?`);
    const info = await stmt.run(...valores, id);

    return info.changes > 0;
  },

  excluir: async (id: number): Promise<boolean> => {
    const stmt = db.prepare('DELETE FROM clientes WHERE id = ?');
    const info = await stmt.run(id);

    return info.changes > 0;
  },

  buscar: async (termo: string): Promise<Cliente[]> => {
    const stmt = db.prepare(`
      SELECT * FROM clientes
      WHERE nome LIKE ? OR email LIKE ? OR telefone LIKE ?
      ORDER BY nome
    `);

    const result = await stmt.all(`%${termo}%`, `%${termo}%`, `%${termo}%`);
    return Array.isArray(result) ? result : [];
  }
};

export default ClienteModel; 