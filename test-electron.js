// Script de teste para verificar se o Electron funciona independentemente
const { app, BrowserWindow } = require('electron');

let mainWindow;

function createWindow() {
  console.log('🧪 TESTE: Criando janela do Electron...');

  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  // Carregar diretamente o localhost (assumindo que React está rodando)
  const PORT = process.env.PORT || '3000';
  const testUrl = `http://localhost:${PORT}`;
  console.log('🧪 TESTE: Carregando URL:', testUrl);
  console.log('🧪 TESTE: Porta configurada:', PORT);

  mainWindow.loadURL(testUrl);

  mainWindow.webContents.openDevTools();

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Log quando a página carregar
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('✅ TESTE: Página carregada com sucesso!');
  });

  // Log se houver erro
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.log('❌ TESTE: Erro ao carregar página:', errorCode, errorDescription);
  });
}

app.on('ready', () => {
  console.log('🧪 TESTE: Electron está pronto!');
  createWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

console.log('🧪 TESTE: Script iniciado. Aguardando Electron...');
