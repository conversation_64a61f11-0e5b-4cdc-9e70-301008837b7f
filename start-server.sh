#!/bin/bash

# Script para iniciar o KHATFIT no servidor Linux
# Este script configura a porta 8080 e inicia o aplicativo

echo "🚀 Iniciando KHATFIT no servidor Linux..."
echo "📍 Porta configurada: 8080"
echo "🌐 URL de acesso: http://localhost:8080"
echo ""

# Verificar se o Node.js está instalado
if ! command -v node &> /dev/null; then
    echo "❌ Node.js não está instalado!"
    echo "   Instale o Node.js primeiro: https://nodejs.org/"
    exit 1
fi

# Verificar se o Yarn está instalado
if ! command -v yarn &> /dev/null; then
    echo "❌ Yarn não está instalado!"
    echo "   Instalando Yarn..."
    npm install -g yarn
fi

# Verificar se as dependências estão instaladas
if [ ! -d "node_modules" ]; then
    echo "📦 Instalando dependências..."
    yarn install
fi

# Copiar configurações do servidor
if [ -f ".env.server" ]; then
    echo "⚙️ Aplicando configurações do servidor..."
    cp .env.server .env
fi

# Verificar se a porta 8080 está disponível
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️ Porta 8080 já está em uso!"
    echo "   Matando processo na porta 8080..."
    lsof -ti:8080 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# Iniciar o aplicativo
echo "🎯 Iniciando aplicativo na porta 8080..."
export PORT=8080
yarn start:server

echo "✅ Aplicativo iniciado com sucesso!"
echo "🌐 Acesse: http://localhost:8080"
