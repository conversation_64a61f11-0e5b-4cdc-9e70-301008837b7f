import React, { ReactNode, useState } from 'react';
import {
  <PERSON>,
  CssB<PERSON>line,
  Divider,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  useTheme,
  useMediaQuery,
  AppBar,
  Toolbar,
  Tooltip,
  Avatar,
  Menu,
  MenuItem
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import PeopleIcon from '@mui/icons-material/People';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import AssessmentIcon from '@mui/icons-material/Assessment';
import HomeIcon from '@mui/icons-material/Home';
import SettingsIcon from '@mui/icons-material/Settings';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import LightModeIcon from '@mui/icons-material/LightMode';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import LogoutIcon from '@mui/icons-material/Logout';
import PersonIcon from '@mui/icons-material/Person';
import { useNavigate, useLocation } from 'react-router-dom';
import GlobalSearch from '../common/GlobalSearch';
import SettingsDialog from '../common/SettingsDialog';
import NotificationCenter from '../common/NotificationCenter';
import KeyboardShortcutsHelp from '../common/KeyboardShortcutsHelp';
import { useSettings } from '../../contexts/SettingsContext';
import { useKeyboardShortcuts } from '../../hooks/useKeyboardShortcuts';
import { useAuth } from '../../contexts/AuthContext';
import { colors } from '../../styles/colors';

const drawerWidth = 240;

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [helpOpen, setHelpOpen] = useState(false);
  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { isDarkMode, toggleTheme } = useSettings();
  const { user, logout } = useAuth();

  // Configurar atalhos de teclado
  useKeyboardShortcuts({
    onToggleSearch: () => {}, // Implementar quando necessário
    onToggleSettings: () => setSettingsOpen(true),
    onToggleTheme: toggleTheme,
    onShowHelp: () => setHelpOpen(true),
    onNewClient: () => navigate('/clientes'),
    onNewAssessment: () => navigate('/avaliacoes'),
    onNewWorkout: () => navigate('/treinos')
  });

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  const handleLogout = () => {
    logout();
    handleUserMenuClose();
    navigate('/login');
  };

  const menuItems = [
    { text: 'Dashboard', icon: <HomeIcon />, path: '/dashboard' },
    { text: 'Clientes', icon: <PeopleIcon />, path: '/clientes' },
    { text: 'Avaliações Físicas', icon: <AssessmentIcon />, path: '/avaliacoes' },
    { text: 'Treinos', icon: <FitnessCenterIcon />, path: '/treinos' },
  ];

  const drawer = (
    <Box sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      bgcolor: colors.ocean,
      color: colors.cloud
    }}>
      <Box sx={{
        p: 2,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        background: colors.ocean,
        position: 'relative'
      }}>
        {/* Botão de menu mobile */}
        <IconButton
          color="inherit"
          aria-label="fechar menu"
          onClick={handleDrawerToggle}
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            display: { md: 'none' },
            color: colors.cloud
          }}
        >
          <MenuIcon />
        </IconButton>

        <Box
          sx={{
            width: 50,
            height: 50,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '50%',
            mb: 1.5,
            background: colors.sea,
            boxShadow: `0px 4px 10px ${colors.sea}4D`
          }}
        >
          <FitnessCenterIcon sx={{ fontSize: 28, color: colors.cloud }} />
        </Box>
        <Typography
          variant="h6"
          sx={{
            fontWeight: 'bold',
            color: colors.cloud,
            fontSize: '1.1rem'
          }}
        >
          Hyper Personal
        </Typography>
      </Box>
      
      <Divider sx={{ mb: 1.5, borderColor: `${colors.cloud}1A` }} />

      <List sx={{ px: 1.5, flexGrow: 1 }}>
        {menuItems.map((item) => {
          const isSelected = location.pathname === item.path;
          return (
            <ListItem key={item.text} disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                selected={isSelected}
                onClick={() => {
                  navigate(item.path);
                  if (isMobile) {
                    setMobileOpen(false);
                  }
                }}
                sx={{
                  borderRadius: 2,
                  py: 1,
                  '&.Mui-selected': {
                    bgcolor: colors.sea,
                    '&:hover': {
                      bgcolor: colors.ocean
                    }
                  },
                  '&:hover': {
                    bgcolor: `${colors.cloud}1A`
                  }
                }}
              >
                <ListItemIcon sx={{
                  color: isSelected ? colors.cloud : `${colors.cloud}B3`,
                  minWidth: 36
                }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText 
                  primary={item.text} 
                  sx={{ 
                    '& .MuiTypography-root': {
                      fontWeight: isSelected ? 600 : 400,
                      color: isSelected ? colors.cloud : `${colors.cloud}B3`
                    }
                  }}
                />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>
      
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="caption" sx={{ color: `${colors.cloud}80` }}>
          Versão 1.0.0
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <CssBaseline />

      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
        aria-label="menu de navegação"
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: drawerWidth,
              boxShadow: '4px 0px 10px rgba(0, 0, 0, 0.1)'
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: drawerWidth,
              boxShadow: '4px 0px 10px rgba(0, 0, 0, 0.05)'
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          display: 'flex',
          flexDirection: 'column',
          minHeight: '100vh',
          bgcolor: '#F8F9FA',
          position: 'relative'
        }}
      >
        {/* Botão de menu flutuante para mobile */}
        <IconButton
          color="primary"
          aria-label="abrir menu"
          onClick={handleDrawerToggle}
          sx={{
            position: 'fixed',
            top: 16,
            left: 16,
            display: { md: 'none' },
            bgcolor: colors.sea,
            color: colors.cloud,
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.15)',
            zIndex: 1200,
            '&:hover': {
              bgcolor: colors.ocean
            }
          }}
        >
          <MenuIcon />
        </IconButton>

        {/* Header com busca global e configurações */}
        <AppBar
          position="static"
          elevation={0}
          sx={{
            bgcolor: 'background.paper',
            borderBottom: 1,
            borderColor: 'divider'
          }}
        >
          <Toolbar sx={{ justifyContent: 'space-between', py: 1, px: { xs: 2, sm: 3 } }}>
            <Box sx={{ flex: 1, maxWidth: 600 }}>
              <GlobalSearch
                placeholder="Buscar clientes, avaliações, treinos..."
                maxResults={6}
                showHistory={true}
              />
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <NotificationCenter onSettingsClick={() => setSettingsOpen(true)} />

              <Tooltip title={isDarkMode ? 'Modo Claro (Ctrl+Shift+D)' : 'Modo Escuro (Ctrl+Shift+D)'}>
                <IconButton onClick={toggleTheme} size="small">
                  {isDarkMode ? <LightModeIcon /> : <DarkModeIcon />}
                </IconButton>
              </Tooltip>

              <Tooltip title="Configurações (Ctrl+,)">
                <IconButton onClick={() => setSettingsOpen(true)} size="small">
                  <SettingsIcon />
                </IconButton>
              </Tooltip>

              {/* Menu do Usuário */}
              <Tooltip title="Perfil do usuário">
                <IconButton onClick={handleUserMenuOpen} size="small">
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      bgcolor: colors.sea,
                      fontSize: '0.875rem'
                    }}
                  >
                    {user?.name?.charAt(0).toUpperCase() || 'U'}
                  </Avatar>
                </IconButton>
              </Tooltip>

              <Menu
                anchorEl={userMenuAnchor}
                open={Boolean(userMenuAnchor)}
                onClose={handleUserMenuClose}
                onClick={handleUserMenuClose}
                PaperProps={{
                  elevation: 8,
                  sx: {
                    overflow: 'visible',
                    filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                    mt: 1.5,
                    minWidth: 200,
                    '& .MuiAvatar-root': {
                      width: 32,
                      height: 32,
                      ml: -0.5,
                      mr: 1,
                    },
                    '&:before': {
                      content: '""',
                      display: 'block',
                      position: 'absolute',
                      top: 0,
                      right: 14,
                      width: 10,
                      height: 10,
                      bgcolor: 'background.paper',
                      transform: 'translateY(-50%) rotate(45deg)',
                      zIndex: 0,
                    },
                  },
                }}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
              >
                <MenuItem onClick={handleUserMenuClose}>
                  <Avatar sx={{ bgcolor: colors.sea, mr: 2 }}>
                    {user?.name?.charAt(0).toUpperCase() || 'U'}
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                      {user?.name || 'Usuário'}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      {user?.email || '<EMAIL>'}
                    </Typography>
                  </Box>
                </MenuItem>
                <Divider />
                <MenuItem onClick={handleUserMenuClose}>
                  <PersonIcon sx={{ mr: 2 }} />
                  Meu Perfil
                </MenuItem>
                <MenuItem onClick={handleLogout}>
                  <LogoutIcon sx={{ mr: 2 }} />
                  Sair
                </MenuItem>
              </Menu>
            </Box>
          </Toolbar>
        </AppBar>

        <Box sx={{ flexGrow: 1, p: { xs: 1, sm: 2, md: 3 } }}>
          {children}
        </Box>

        {/* Dialog de Configurações */}
        <SettingsDialog
          open={settingsOpen}
          onClose={() => setSettingsOpen(false)}
        />

        {/* Ajuda de Atalhos de Teclado */}
        <KeyboardShortcutsHelp
          open={helpOpen}
          onClose={() => setHelpOpen(false)}
        />
      </Box>
    </Box>
  );
};

export default Layout; 