// Script para iniciar Electron após delay
const { spawn } = require('child_process');
const http = require('http');

console.log('⏳ Aguardando React carregar (60 segundos)...');

function checkReactHealth() {
  return new Promise((resolve) => {
    const PORT = process.env.PORT || '3000';
    const url = `http://localhost:${PORT}`;
    console.log('🔍 Verificando saúde do React em:', url);

    const req = http.get(url, (res) => {
      resolve(res.statusCode === 200);
    });

    req.on('error', () => {
      resolve(false);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      resolve(false);
    });
  });
}

setTimeout(async () => {
  console.log('🔍 Verificando se React está pronto...');

  const isReady = await checkReactHealth();

  if (isReady) {
    console.log('✅ React está pronto!');
  } else {
    console.log('⚠️ React ainda não está pronto, mas continuando...');
  }

  console.log('🖥️ Iniciando Electron...');

  const electron = spawn('npx', ['electron', '.'], {
    stdio: 'inherit',
    shell: true
  });

  electron.on('close', (code) => {
    console.log(`✅ Electron finalizado com código: ${code}`);
  });

  electron.on('error', (err) => {
    console.error('❌ Erro ao iniciar Electron:', err);
  });

}, 60000); // 60 segundos
