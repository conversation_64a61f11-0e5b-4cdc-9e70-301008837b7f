import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Checkbox,
  Alert,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Tooltip,
  Chip
} from '@mui/material';
import {
  Backup as BackupIcon,
  Restore as RestoreIcon,
  CloudDownload as DownloadIcon,
  CloudUpload as UploadIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Info as InfoIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { BackupService, BackupData, BackupOptions } from '../../services/BackupService';
import { useAppToast } from '../../contexts/ToastContext';
import { colors } from '../../styles/colors';
import { AnimatedBox, AnimatedButton } from './AnimatedComponents';

// ============================================================================
// BACKUP MANAGER - INTERFACE PARA BACKUP E RESTORE
// ============================================================================

interface BackupManagerProps {
  open: boolean;
  onClose: () => void;
}

export const BackupManager: React.FC<BackupManagerProps> = ({ open, onClose }) => {
  const toast = useAppToast();
  const [loading, setLoading] = useState(false);
  const [backupOptions, setBackupOptions] = useState<BackupOptions>({
    includeClientes: true,
    includeAvaliacoes: true,
    includeTreinos: true,
    compressData: false
  });
  const [restoreFile, setRestoreFile] = useState<File | null>(null);
  const [backupPreview, setBackupPreview] = useState<BackupData | null>(null);
  const [confirmRestore, setConfirmRestore] = useState(false);

  const handleCreateBackup = async () => {
    setLoading(true);
    try {
      await BackupService.downloadBackup(backupOptions);
      toast.showSuccess('Backup criado e baixado com sucesso!');
      onClose();
    } catch (error) {
      console.error('Erro ao criar backup:', error);
      toast.showError('Erro ao criar backup. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setLoading(true);
    try {
      const backupData = await BackupService.processarArquivoBackup(file);
      setRestoreFile(file);
      setBackupPreview(backupData);
      toast.showSuccess('Arquivo de backup carregado com sucesso!');
    } catch (error) {
      console.error('Erro ao processar arquivo:', error);
      toast.showError('Arquivo de backup inválido ou corrompido.');
    } finally {
      setLoading(false);
    }
  };

  const handleRestore = async () => {
    if (!backupPreview) return;

    setLoading(true);
    try {
      await BackupService.restaurarBackup(backupPreview, {
        limparDadosExistentes: confirmRestore,
        validarIntegridade: true
      });
      toast.showSuccess('Dados restaurados com sucesso!');
      onClose();
      // Recarregar a página para atualizar todos os dados
      window.location.reload();
    } catch (error) {
      console.error('Erro ao restaurar backup:', error);
      toast.showError('Erro ao restaurar backup. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString('pt-BR');
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 4,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)'
        }
      }}
    >
      <DialogTitle sx={{ 
        bgcolor: colors.professional[50], 
        color: colors.professional[700],
        display: 'flex',
        alignItems: 'center',
        gap: 2
      }}>
        <BackupIcon />
        Gerenciamento de Backup
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {loading && (
          <Box sx={{ mb: 3 }}>
            <LinearProgress sx={{ borderRadius: 2 }} />
            <Typography variant="body2" sx={{ mt: 1, textAlign: 'center', color: colors.professional[600] }}>
              Processando...
            </Typography>
          </Box>
        )}

        {/* Seção de Criar Backup */}
        <AnimatedBox animation="fadeInUp" delay={0.1}>
          <Paper elevation={0} sx={{ p: 3, mb: 3, border: `1px solid ${colors.professional[200]}`, borderRadius: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <DownloadIcon sx={{ color: colors.energy[500], mr: 1 }} />
              <Typography variant="h6" sx={{ color: colors.professional[700] }}>
                Criar Backup
              </Typography>
            </Box>

            <Typography variant="body2" sx={{ color: colors.professional[600], mb: 3 }}>
              Exporte todos os seus dados para um arquivo de backup seguro.
            </Typography>

            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" sx={{ mb: 2, color: colors.professional[700] }}>
                Selecione os dados para incluir:
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={backupOptions.includeClientes}
                      onChange={(e) => setBackupOptions(prev => ({ ...prev, includeClientes: e.target.checked }))}
                      sx={{ color: colors.energy[500] }}
                    />
                  }
                  label="Dados dos Clientes"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={backupOptions.includeAvaliacoes}
                      onChange={(e) => setBackupOptions(prev => ({ ...prev, includeAvaliacoes: e.target.checked }))}
                      sx={{ color: colors.health[500] }}
                    />
                  }
                  label="Avaliações Físicas"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={backupOptions.includeTreinos}
                      onChange={(e) => setBackupOptions(prev => ({ ...prev, includeTreinos: e.target.checked }))}
                      sx={{ color: colors.professional[500] }}
                    />
                  }
                  label="Treinos e Exercícios"
                />
              </Box>
            </Box>

            <AnimatedButton
              hoverScale={1.02}
              onClick={handleCreateBackup}
              disabled={loading || (!backupOptions.includeClientes && !backupOptions.includeAvaliacoes && !backupOptions.includeTreinos)}
              sx={{
                bgcolor: colors.energy[500],
                color: 'white',
                px: 3,
                py: 1.5,
                borderRadius: 3,
                '&:hover': {
                  bgcolor: colors.energy[600]
                },
                '&:disabled': {
                  bgcolor: colors.professional[200]
                }
              }}
            >
              <DownloadIcon sx={{ mr: 1 }} />
              Criar e Baixar Backup
            </AnimatedButton>
          </Paper>
        </AnimatedBox>

        <Divider sx={{ my: 3, borderColor: colors.professional[200] }} />

        {/* Seção de Restaurar Backup */}
        <AnimatedBox animation="fadeInUp" delay={0.2}>
          <Paper elevation={0} sx={{ p: 3, border: `1px solid ${colors.professional[200]}`, borderRadius: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <UploadIcon sx={{ color: colors.health[500], mr: 1 }} />
              <Typography variant="h6" sx={{ color: colors.professional[700] }}>
                Restaurar Backup
              </Typography>
            </Box>

            <Typography variant="body2" sx={{ color: colors.professional[600], mb: 3 }}>
              Importe dados de um arquivo de backup anterior.
            </Typography>

            {!backupPreview ? (
              <Box>
                <input
                  accept=".json"
                  style={{ display: 'none' }}
                  id="backup-file-input"
                  type="file"
                  onChange={handleFileSelect}
                  disabled={loading}
                />
                <label htmlFor="backup-file-input">
                  <AnimatedButton
                    component="span"
                    hoverScale={1.02}
                    disabled={loading}
                    sx={{
                      bgcolor: colors.health[500],
                      color: 'white',
                      px: 3,
                      py: 1.5,
                      borderRadius: 3,
                      cursor: 'pointer',
                      '&:hover': {
                        bgcolor: colors.health[600]
                      },
                      '&:disabled': {
                        bgcolor: colors.professional[200]
                      }
                    }}
                  >
                    <UploadIcon sx={{ mr: 1 }} />
                    Selecionar Arquivo de Backup
                  </AnimatedButton>
                </label>
              </Box>
            ) : (
              <Box>
                <Alert severity="info" sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Arquivo de backup carregado:
                  </Typography>
                  <Typography variant="body2">
                    <strong>Arquivo:</strong> {restoreFile?.name}<br />
                    <strong>Tamanho:</strong> {restoreFile ? formatFileSize(restoreFile.size) : 'N/A'}<br />
                    <strong>Data do backup:</strong> {formatDate(backupPreview.timestamp)}<br />
                    <strong>Versão:</strong> {backupPreview.version}
                  </Typography>
                </Alert>

                <Typography variant="subtitle2" sx={{ mb: 2, color: colors.professional[700] }}>
                  Dados encontrados no backup:
                </Typography>

                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <CheckIcon sx={{ color: colors.energy[500] }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary={`${backupPreview.metadata.totalClientes} Clientes`}
                      secondary="Dados pessoais e informações de contato"
                    />
                    <Chip 
                      label={backupPreview.metadata.totalClientes} 
                      size="small" 
                      sx={{ bgcolor: colors.energy[50], color: colors.energy[700] }}
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon>
                      <CheckIcon sx={{ color: colors.health[500] }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary={`${backupPreview.metadata.totalAvaliacoes} Avaliações Físicas`}
                      secondary="Dados antropométricos e composição corporal"
                    />
                    <Chip 
                      label={backupPreview.metadata.totalAvaliacoes} 
                      size="small" 
                      sx={{ bgcolor: colors.health[50], color: colors.health[700] }}
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon>
                      <CheckIcon sx={{ color: colors.professional[500] }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary={`${backupPreview.metadata.totalTreinos} Treinos`}
                      secondary={`${backupPreview.metadata.totalExercicios} exercícios e ${backupPreview.metadata.totalSeries} séries`}
                    />
                    <Chip 
                      label={backupPreview.metadata.totalTreinos} 
                      size="small" 
                      sx={{ bgcolor: colors.professional[50], color: colors.professional[700] }}
                    />
                  </ListItem>
                </List>

                <Alert severity="warning" sx={{ mt: 3, mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Atenção:</strong> A restauração irá adicionar os dados do backup aos dados existentes. 
                    Marque a opção abaixo apenas se desejar substituir completamente todos os dados atuais.
                  </Typography>
                </Alert>

                <FormControlLabel
                  control={
                    <Checkbox
                      checked={confirmRestore}
                      onChange={(e) => setConfirmRestore(e.target.checked)}
                      sx={{ color: colors.error }}
                    />
                  }
                  label="Limpar todos os dados existentes antes da restauração"
                  sx={{ mb: 3 }}
                />

                <Box sx={{ display: 'flex', gap: 2 }}>
                  <AnimatedButton
                    hoverScale={1.02}
                    onClick={handleRestore}
                    disabled={loading}
                    sx={{
                      bgcolor: colors.health[500],
                      color: 'white',
                      px: 3,
                      py: 1.5,
                      borderRadius: 3,
                      '&:hover': {
                        bgcolor: colors.health[600]
                      }
                    }}
                  >
                    <RestoreIcon sx={{ mr: 1 }} />
                    Restaurar Dados
                  </AnimatedButton>

                  <Button
                    onClick={() => {
                      setBackupPreview(null);
                      setRestoreFile(null);
                      setConfirmRestore(false);
                    }}
                    sx={{ color: colors.professional[600] }}
                  >
                    Cancelar
                  </Button>
                </Box>
              </Box>
            )}
          </Paper>
        </AnimatedBox>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button onClick={onClose} sx={{ color: colors.professional[600] }}>
          Fechar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BackupManager;
