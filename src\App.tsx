import React, { useEffect } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { ptBR } from '@mui/material/locale';
import { AuthProvider } from './contexts/AuthContext';
import { AppProvider } from './contexts/AppContext';
import { AvaliacaoFisicaProvider } from './contexts/AvaliacaoFisicaContext';
import TreinoProvider from './contexts/TreinoContext';
import { ClienteProvider } from './contexts/ClienteContext';
import { ToastProvider } from './contexts/ToastContext';
import { SettingsProvider } from './contexts/SettingsContext';
import { NotificationProvider } from './contexts/NotificationContext';
import ThemeWrapper from './components/ThemeWrapper';
import Layout from './components/Layout/Layout';
import { ProtectedRoute, PublicRoute } from './components/auth/ProtectedRoute';
import LoginPage, { RegisterPage } from './pages/LoginPage';
import HomePage from './pages/HomePage';
import ClientesPage from './pages/ClientesPage';
import AvaliacoesPage from './pages/AvaliacoesPage';
import TreinosPage from './pages/TreinosPage';
import NotFoundPage from './pages/NotFoundPage';
import { initializeDatabase } from './db/init';
import { PageTransition } from './components/common/AnimatedComponents';
import theme from './styles/theme';

function App() {
  useEffect(() => {
    initializeDatabase();
  }, []);

  return (
    <SettingsProvider>
      <ThemeWrapper>
        <ToastProvider>
          <NotificationProvider>
            <AuthProvider>
              <BrowserRouter>
                <Routes>
                  {/* Rotas Públicas */}
                  <Route
                    path="/login"
                    element={
                      <PublicRoute>
                        <LoginPage />
                      </PublicRoute>
                    }
                  />
                  <Route
                    path="/register"
                    element={
                      <PublicRoute>
                        <RegisterPage />
                      </PublicRoute>
                    }
                  />

                  {/* Rotas Protegidas */}
                  <Route
                    path="/dashboard"
                    element={
                      <ProtectedRoute>
                        <AppProvider>
                          <ClienteProvider>
                            <AvaliacaoFisicaProvider>
                              <TreinoProvider>
                                <Layout>
                                  <HomePage />
                                </Layout>
                              </TreinoProvider>
                            </AvaliacaoFisicaProvider>
                          </ClienteProvider>
                        </AppProvider>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/clientes"
                    element={
                      <ProtectedRoute>
                        <AppProvider>
                          <ClienteProvider>
                            <AvaliacaoFisicaProvider>
                              <TreinoProvider>
                                <Layout>
                                  <ClientesPage />
                                </Layout>
                              </TreinoProvider>
                            </AvaliacaoFisicaProvider>
                          </ClienteProvider>
                        </AppProvider>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/avaliacoes"
                    element={
                      <ProtectedRoute>
                        <AppProvider>
                          <ClienteProvider>
                            <AvaliacaoFisicaProvider>
                              <TreinoProvider>
                                <Layout>
                                  <AvaliacoesPage />
                                </Layout>
                              </TreinoProvider>
                            </AvaliacaoFisicaProvider>
                          </ClienteProvider>
                        </AppProvider>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/treinos"
                    element={
                      <ProtectedRoute>
                        <AppProvider>
                          <ClienteProvider>
                            <AvaliacaoFisicaProvider>
                              <TreinoProvider>
                                <Layout>
                                  <TreinosPage />
                                </Layout>
                              </TreinoProvider>
                            </AvaliacaoFisicaProvider>
                          </ClienteProvider>
                        </AppProvider>
                      </ProtectedRoute>
                    }
                  />

                  {/* Rota raiz redireciona para dashboard */}
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />

                  {/* 404 */}
                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </BrowserRouter>
            </AuthProvider>
          </NotificationProvider>
        </ToastProvider>
      </ThemeWrapper>
    </SettingsProvider>
  );
}

export default App;
