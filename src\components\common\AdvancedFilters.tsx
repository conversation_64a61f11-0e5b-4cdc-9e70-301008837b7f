import React, { useState } from 'react';
import {
  Box,
  Paper,
  TextField,
  InputAdornment,
  Chip,
  Button,
  Menu,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Typography,
  Divider,
  IconButton,
  Collapse,
  Stack,
  Autocomplete
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Tune as TuneIcon
} from '@mui/icons-material';
import { colors } from '../../styles/colors';
import { AnimatedBox, AnimatedButton } from './AnimatedComponents';

// ============================================================================
// ADVANCED FILTERS - FILTROS AVANÇADOS COM CHIPS VISUAIS
// ============================================================================

export interface FilterOption {
  id: string;
  label: string;
  value: any;
  type: 'text' | 'select' | 'date' | 'number' | 'boolean';
  options?: Array<{ label: string; value: any }>;
}

export interface ActiveFilter {
  id: string;
  label: string;
  value: any;
  displayValue: string;
}

interface AdvancedFiltersProps {
  searchPlaceholder?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  filterOptions?: FilterOption[];
  activeFilters?: ActiveFilter[];
  onFiltersChange?: (filters: ActiveFilter[]) => void;
  onClearAll?: () => void;
  showFilterCount?: boolean;
  compact?: boolean;
}

export const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  searchPlaceholder = 'Buscar...',
  searchValue = '',
  onSearchChange,
  filterOptions = [],
  activeFilters = [],
  onFiltersChange,
  onClearAll,
  showFilterCount = true,
  compact = false
}) => {
  const [filtersExpanded, setFiltersExpanded] = useState(false);
  const [filterMenuAnchor, setFilterMenuAnchor] = useState<null | HTMLElement>(null);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChange?.(event.target.value);
  };

  const handleAddFilter = (option: FilterOption, value: any) => {
    if (!value || value === '') return;

    const displayValue = option.type === 'select' 
      ? option.options?.find(opt => opt.value === value)?.label || value
      : value.toString();

    const newFilter: ActiveFilter = {
      id: option.id,
      label: option.label,
      value,
      displayValue
    };

    const updatedFilters = activeFilters.filter(f => f.id !== option.id);
    updatedFilters.push(newFilter);
    onFiltersChange?.(updatedFilters);
    setFilterMenuAnchor(null);
  };

  const handleRemoveFilter = (filterId: string) => {
    const updatedFilters = activeFilters.filter(f => f.id !== filterId);
    onFiltersChange?.(updatedFilters);
  };

  const handleClearAll = () => {
    onClearAll?.();
    onFiltersChange?.([]);
  };

  const renderFilterOption = (option: FilterOption) => {
    switch (option.type) {
      case 'select':
        return (
          <FormControl fullWidth size="small" sx={{ mb: 2 }}>
            <InputLabel>{option.label}</InputLabel>
            <Select
              label={option.label}
              onChange={(e) => handleAddFilter(option, e.target.value)}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '&.Mui-focused fieldset': {
                    borderColor: colors.energy[500],
                  }
                }
              }}
            >
              {option.options?.map((opt) => (
                <MenuItem key={opt.value} value={opt.value}>
                  {opt.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case 'date':
        return (
          <TextField
            fullWidth
            size="small"
            type="date"
            label={option.label}
            InputLabelProps={{ shrink: true }}
            onChange={(e) => handleAddFilter(option, e.target.value)}
            sx={{ 
              mb: 2,
              '& .MuiOutlinedInput-root': {
                '&.Mui-focused fieldset': {
                  borderColor: colors.energy[500],
                }
              }
            }}
          />
        );

      case 'number':
        return (
          <TextField
            fullWidth
            size="small"
            type="number"
            label={option.label}
            onChange={(e) => handleAddFilter(option, e.target.value)}
            sx={{ 
              mb: 2,
              '& .MuiOutlinedInput-root': {
                '&.Mui-focused fieldset': {
                  borderColor: colors.energy[500],
                }
              }
            }}
          />
        );

      default:
        return (
          <TextField
            fullWidth
            size="small"
            label={option.label}
            onChange={(e) => handleAddFilter(option, e.target.value)}
            sx={{ 
              mb: 2,
              '& .MuiOutlinedInput-root': {
                '&.Mui-focused fieldset': {
                  borderColor: colors.energy[500],
                }
              }
            }}
          />
        );
    }
  };

  return (
    <AnimatedBox animation="fadeInUp" duration={0.4}>
      <Paper
        elevation={0}
        sx={{
          p: compact ? 2 : 3,
          borderRadius: 4,
          border: `1px solid ${colors.professional[200]}`,
          bgcolor: colors.white,
          mb: 3
        }}
      >
        {/* Barra de busca principal */}
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', mb: 2 }}>
          <TextField
            fullWidth
            size="small"
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: colors.energy[500] }} />
                </InputAdornment>
              ),
              sx: {
                borderRadius: 3,
                '& fieldset': {
                  borderColor: colors.professional[300],
                },
                '&:hover fieldset': {
                  borderColor: colors.energy[400],
                },
                '&.Mui-focused fieldset': {
                  borderColor: colors.energy[500],
                }
              }
            }}
          />

          {/* Botão de filtros */}
          <AnimatedButton
            hoverScale={1.05}
            onClick={(e) => setFilterMenuAnchor(e.currentTarget)}
            sx={{
              minWidth: 'auto',
              p: 1.5,
              borderRadius: 3,
              bgcolor: colors.professional[50],
              border: `1px solid ${colors.professional[300]}`,
              color: colors.professional[700],
              '&:hover': {
                bgcolor: colors.energy[50],
                borderColor: colors.energy[300],
                color: colors.energy[600]
              }
            }}
          >
            <TuneIcon />
          </AnimatedButton>

          {/* Botão expandir/recolher filtros */}
          {filterOptions.length > 0 && (
            <IconButton
              onClick={() => setFiltersExpanded(!filtersExpanded)}
              sx={{
                color: colors.professional[600],
                '&:hover': {
                  bgcolor: colors.professional[50]
                }
              }}
            >
              {filtersExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          )}
        </Box>

        {/* Chips de filtros ativos */}
        {activeFilters.length > 0 && (
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <Typography variant="body2" sx={{ color: colors.professional[600], fontWeight: 500 }}>
                Filtros ativos {showFilterCount && `(${activeFilters.length})`}:
              </Typography>
              <Button
                size="small"
                onClick={handleClearAll}
                sx={{
                  color: colors.professional[500],
                  fontSize: '0.75rem',
                  textTransform: 'none',
                  minWidth: 'auto',
                  p: 0.5,
                  '&:hover': {
                    bgcolor: colors.professional[50]
                  }
                }}
              >
                Limpar todos
              </Button>
            </Box>
            
            <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
              {activeFilters.map((filter) => (
                <AnimatedBox key={filter.id} animation="scaleIn" duration={0.3}>
                  <Chip
                    label={`${filter.label}: ${filter.displayValue}`}
                    onDelete={() => handleRemoveFilter(filter.id)}
                    deleteIcon={<ClearIcon />}
                    sx={{
                      bgcolor: colors.energy[50],
                      color: colors.energy[700],
                      borderRadius: 2,
                      '& .MuiChip-deleteIcon': {
                        color: colors.energy[500],
                        '&:hover': {
                          color: colors.energy[700]
                        }
                      },
                      '&:hover': {
                        bgcolor: colors.energy[100]
                      }
                    }}
                  />
                </AnimatedBox>
              ))}
            </Stack>
          </Box>
        )}

        {/* Filtros expandidos */}
        <Collapse in={filtersExpanded}>
          <Divider sx={{ mb: 2, borderColor: colors.professional[200] }} />
          <Typography variant="h6" sx={{ mb: 2, color: colors.professional[700] }}>
            Filtros Avançados
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2 }}>
            {filterOptions.map((option) => (
              <Box key={option.id}>
                {renderFilterOption(option)}
              </Box>
            ))}
          </Box>
        </Collapse>

        {/* Menu de filtros rápidos */}
        <Menu
          anchorEl={filterMenuAnchor}
          open={Boolean(filterMenuAnchor)}
          onClose={() => setFilterMenuAnchor(null)}
          PaperProps={{
            sx: {
              borderRadius: 3,
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
              border: `1px solid ${colors.professional[200]}`,
              minWidth: 250
            }
          }}
        >
          <Box sx={{ p: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 1, color: colors.professional[700] }}>
              Filtros Rápidos
            </Typography>
            {filterOptions.slice(0, 5).map((option) => (
              <Box key={option.id} sx={{ mb: 1 }}>
                {renderFilterOption(option)}
              </Box>
            ))}
          </Box>
        </Menu>
      </Paper>
    </AnimatedBox>
  );
};

export default AdvancedFilters;
