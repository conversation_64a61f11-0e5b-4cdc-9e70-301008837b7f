import React from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  <PERSON>alogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Box,
  Chip,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  Divider,
  IconButton
} from '@mui/material';
import {
  Keyboard as KeyboardIcon,
  Navigation as NavigationIcon,
  Search as SearchIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useKeyboardShortcuts, KeyboardShortcut } from '../../hooks/useKeyboardShortcuts';
import { colors } from '../../styles/colors';
import { AnimatedBox } from './AnimatedComponents';

// ============================================================================
// KEYBOARD SHORTCUTS HELP - AJUDA DOS ATALHOS DE TECLADO
// ============================================================================

interface KeyboardShortcutsHelpProps {
  open: boolean;
  onClose: () => void;
}

export const KeyboardShortcutsHelp: React.FC<KeyboardShortcutsHelpProps> = ({ open, onClose }) => {
  const { shortcuts, getShortcutsByCategory, formatShortcut } = useKeyboardShortcuts();

  const categoryIcons = {
    navigation: <NavigationIcon sx={{ color: colors.energy[500] }} />,
    search: <SearchIcon sx={{ color: colors.health[500] }} />,
    actions: <AddIcon sx={{ color: colors.professional[500] }} />,
    general: <SettingsIcon sx={{ color: colors.professional[600] }} />
  };

  const categoryTitles = {
    navigation: 'Navegação',
    search: 'Busca',
    actions: 'Ações Rápidas',
    general: 'Geral'
  };

  const categoryDescriptions = {
    navigation: 'Navegue rapidamente entre as páginas',
    search: 'Acesse a busca global instantaneamente',
    actions: 'Crie novos registros com atalhos',
    general: 'Configurações e funcionalidades gerais'
  };

  const renderShortcutChip = (shortcut: KeyboardShortcut) => {
    const formattedShortcut = formatShortcut(shortcut);
    const keys = formattedShortcut.split(' + ');
    
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        {keys.map((key, index) => (
          <React.Fragment key={index}>
            <Chip
              label={key}
              size="small"
              sx={{
                bgcolor: colors.professional[100],
                color: colors.professional[700],
                fontFamily: 'monospace',
                fontWeight: 600,
                fontSize: '0.75rem',
                height: 24,
                '& .MuiChip-label': {
                  px: 1
                }
              }}
            />
            {index < keys.length - 1 && (
              <Typography variant="caption" sx={{ color: colors.professional[500], mx: 0.5 }}>
                +
              </Typography>
            )}
          </React.Fragment>
        ))}
      </Box>
    );
  };

  const renderCategorySection = (category: KeyboardShortcut['category']) => {
    const categoryShortcuts = getShortcutsByCategory(category);
    
    if (categoryShortcuts.length === 0) return null;

    return (
      <AnimatedBox key={category} animation="fadeInUp" delay={0.1}>
        <Card elevation={0} sx={{ mb: 3, border: `1px solid ${colors.professional[200]}` }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              {categoryIcons[category]}
              <Box sx={{ ml: 2 }}>
                <Typography variant="h6" sx={{ color: colors.professional[700], fontWeight: 600 }}>
                  {categoryTitles[category]}
                </Typography>
                <Typography variant="body2" sx={{ color: colors.professional[500] }}>
                  {categoryDescriptions[category]}
                </Typography>
              </Box>
            </Box>
            
            <List dense sx={{ py: 0 }}>
              {categoryShortcuts.map((shortcut, index) => (
                <React.Fragment key={index}>
                  <ListItem sx={{ px: 0, py: 1 }}>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="body2" sx={{ color: colors.professional[700] }}>
                            {shortcut.description}
                          </Typography>
                          {renderShortcutChip(shortcut)}
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < categoryShortcuts.length - 1 && (
                    <Divider sx={{ borderColor: colors.professional[100] }} />
                  )}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      </AnimatedBox>
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 4,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
          maxHeight: '80vh'
        }
      }}
    >
      <DialogTitle sx={{ 
        bgcolor: colors.professional[50], 
        color: colors.professional[700],
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        pb: 2
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <KeyboardIcon />
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Atalhos de Teclado
            </Typography>
            <Typography variant="body2" sx={{ color: colors.professional[600] }}>
              Navegue mais rapidamente com atalhos de teclado
            </Typography>
          </Box>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {/* Dica principal */}
        <AnimatedBox animation="fadeInUp" delay={0.05}>
          <Box sx={{ 
            bgcolor: colors.energy[50], 
            border: `1px solid ${colors.energy[200]}`,
            borderRadius: 3,
            p: 2,
            mb: 3
          }}>
            <Typography variant="body2" sx={{ color: colors.energy[700], fontWeight: 500 }}>
              💡 <strong>Dica:</strong> Pressione <Chip label="?" size="small" sx={{ mx: 0.5, bgcolor: colors.energy[100] }} /> 
              ou <Chip label="F1" size="small" sx={{ mx: 0.5, bgcolor: colors.energy[100] }} /> 
              a qualquer momento para ver esta ajuda.
            </Typography>
          </Box>
        </AnimatedBox>

        {/* Seções por categoria */}
        <Grid container spacing={0}>
          <Grid item xs={12}>
            {(['navigation', 'search', 'actions', 'general'] as const).map(category => 
              renderCategorySection(category)
            )}
          </Grid>
        </Grid>

        {/* Informações adicionais */}
        <AnimatedBox animation="fadeInUp" delay={0.3}>
          <Box sx={{ 
            bgcolor: colors.professional[50], 
            border: `1px solid ${colors.professional[200]}`,
            borderRadius: 3,
            p: 2,
            mt: 2
          }}>
            <Typography variant="subtitle2" sx={{ color: colors.professional[700], mb: 1, fontWeight: 600 }}>
              Notas Importantes:
            </Typography>
            <Typography variant="body2" sx={{ color: colors.professional[600], mb: 1 }}>
              • Os atalhos funcionam em qualquer lugar do sistema, exceto quando você está digitando em campos de texto
            </Typography>
            <Typography variant="body2" sx={{ color: colors.professional[600], mb: 1 }}>
              • Use <strong>Ctrl</strong> no Windows/Linux ou <strong>Cmd</strong> no Mac para atalhos com modificadores
            </Typography>
            <Typography variant="body2" sx={{ color: colors.professional[600] }}>
              • Pressione <strong>Escape</strong> para fechar qualquer modal ou dialog aberto
            </Typography>
          </Box>
        </AnimatedBox>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button onClick={onClose} variant="contained" sx={{ borderRadius: 3 }}>
          Entendi
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default KeyboardShortcutsHelp;
