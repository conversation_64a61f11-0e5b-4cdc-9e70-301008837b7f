# 🖥️ Configuração para Servidor Linux

Este guia explica como configurar e executar o Personal Trainer App em um servidor Linux com uma porta personalizada.

## 🚀 Início Rápido

### 1. Verificar e Configurar Porta Automaticamente
```bash
# Dar permissão aos scripts
chmod +x *.sh

# Verificar portas disponíveis e configurar automaticamente
./check-port.sh
```

### 2. Iniciar o Aplicativo
```bash
# Apenas servidor web (recomendado para servidores)
./start-server.sh

# Ou com Electron (se houver interface gráfica)
./start-server-dev.sh
```

## 🔧 Configuração Manual

### Opção 1: Usar Porta 8080 (Recomendado)
```bash
# Configurar porta 8080
export PORT=8080

# Iniciar aplicativo
yarn start:server
```

### Opção 2: Usar Porta Personalizada
```bash
# Configurar porta personalizada (exemplo: 9000)
export PORT=9000

# Iniciar aplicativo
yarn start
```

### Opção 3: Editar Arquivo .env
```bash
# Editar arquivo .env
nano .env

# Alterar a linha PORT=3000 para PORT=8080
# Salvar e sair

# Iniciar aplicativo
yarn start
```

## 📋 Scripts Disponíveis

| Script | Descrição |
|--------|-----------|
| `./check-port.sh` | Verifica portas disponíveis e configura automaticamente |
| `./start-server.sh` | Inicia apenas o servidor web (sem Electron) |
| `./start-server-dev.sh` | Inicia servidor + Electron (se disponível) |
| `yarn start:server` | Inicia na porta 8080 |
| `yarn start:8080` | Inicia na porta 8080 |
| `yarn server` | Alias para start:server |

## 🌐 Portas Recomendadas

1. **8080** - Primeira opção (mais comum)
2. **8081** - Segunda opção
3. **8082** - Terceira opção
4. **3000** - Porta padrão (pode estar ocupada)
5. **5000** - Alternativa comum

## 🔍 Verificar Portas Ocupadas

```bash
# Verificar porta específica
lsof -i :8080

# Verificar todas as portas em uso
netstat -tulpn | grep LISTEN

# Matar processo em porta específica
lsof -ti:8080 | xargs kill -9
```

## 🐛 Solução de Problemas

### Erro: "Port 8080 is already in use"
```bash
# Verificar o que está usando a porta
lsof -i :8080

# Matar o processo
lsof -ti:8080 | xargs kill -9

# Ou usar outra porta
PORT=8081 yarn start
```

### Erro: "yarn: command not found"
```bash
# Instalar Yarn
npm install -g yarn

# Ou usar npm diretamente
npm run start:server
```

### Erro: "Permission denied"
```bash
# Dar permissão aos scripts
chmod +x *.sh

# Ou executar com bash
bash start-server.sh
```

## 🔒 Configurações de Firewall

Se estiver usando firewall, libere a porta:

```bash
# UFW (Ubuntu)
sudo ufw allow 8080

# iptables
sudo iptables -A INPUT -p tcp --dport 8080 -j ACCEPT

# firewalld (CentOS/RHEL)
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

## 📊 Monitoramento

```bash
# Verificar se o aplicativo está rodando
curl http://localhost:8080

# Verificar logs
yarn start:server 2>&1 | tee app.log

# Monitorar em tempo real
tail -f app.log
```

## 🔄 Reinicialização Automática

Para reiniciar automaticamente em caso de falha, use PM2:

```bash
# Instalar PM2
npm install -g pm2

# Criar arquivo de configuração
echo '{
  "name": "khatfit-app",
  "script": "yarn",
  "args": "start:server",
  "env": {
    "PORT": "8080"
  }
}' > ecosystem.config.json

# Iniciar com PM2
pm2 start ecosystem.config.json

# Configurar para iniciar no boot
pm2 startup
pm2 save
```

## 📝 Logs e Debug

```bash
# Logs detalhados
DEBUG=* yarn start:server

# Apenas logs do aplicativo
yarn start:server > app.log 2>&1

# Verificar performance
NODE_ENV=production yarn start:server
```
