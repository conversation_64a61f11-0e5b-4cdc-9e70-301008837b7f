import { AvaliacaoFisica } from '../models/AvaliacaoFisica';
import { Cliente } from '../models/Cliente';
import { Treino, Exercicio, Serie } from '../models/Treino';
import { formatarData } from '../utils/formatadores';
import { colors } from '../styles/colors';

// ============================================================================
// PRINT SERVICE - GERAÇÃO DE RELATÓRIOS PARA IMPRESSÃO HTML
// ============================================================================

export class PrintService {
  private static readonly PRINT_STYLES = `
    @media print {
      @page {
        size: A4;
        margin: 2cm;
      }
      
      body {
        font-family: 'Arial', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #333;
        background: white;
      }
      
      .print-container {
        max-width: 100%;
        margin: 0;
        padding: 0;
      }
      
      .print-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid ${colors.professional[500]};
        padding-bottom: 20px;
      }
      
      .print-title {
        font-size: 24px;
        font-weight: bold;
        color: ${colors.professional[500]};
        margin-bottom: 10px;
      }
      
      .print-subtitle {
        font-size: 14px;
        color: #666;
      }
      
      .print-section {
        margin-bottom: 25px;
        page-break-inside: avoid;
      }
      
      .print-section-title {
        font-size: 16px;
        font-weight: bold;
        color: ${colors.ocean};
        margin-bottom: 15px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 5px;
      }
      
      .print-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }
      
      .print-table th,
      .print-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }
      
      .print-table th {
        background-color: ${colors.professional[100]};
        font-weight: bold;
      }
      
      .print-info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
      }
      
      .print-info-item {
        margin-bottom: 10px;
      }
      
      .print-info-label {
        font-weight: bold;
        color: ${colors.ocean};
      }
      
      .print-footer {
        position: fixed;
        bottom: 1cm;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 10px;
        color: #666;
        border-top: 1px solid #ddd;
        padding-top: 10px;
      }
      
      .no-print {
        display: none !important;
      }
      
      .page-break {
        page-break-before: always;
      }
    }
    
    @media screen {
      .print-preview {
        max-width: 800px;
        margin: 20px auto;
        padding: 40px;
        background: white;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-radius: 8px;
      }
    }
  `;

  // --------------------------------------------------------------------------
  // RELATÓRIO DE AVALIAÇÃO FÍSICA COMPLETO
  // --------------------------------------------------------------------------
  static async gerarRelatorioAvaliacaoFisica(
    avaliacao: AvaliacaoFisica, 
    cliente: Cliente
  ): Promise<void> {
    const html = this.criarHTMLAvaliacaoFisica(avaliacao, cliente);
    this.abrirJanelaImpressao(html, `Avaliacao_Fisica_${cliente.nome.replace(/\s+/g, '_')}`);
  }

  // --------------------------------------------------------------------------
  // FICHA DE TREINO PARA IMPRESSÃO
  // --------------------------------------------------------------------------
  static async gerarFichaTreino(
    treino: Treino, 
    cliente: Cliente, 
    exercicios: Exercicio[],
    series: { [exercicioId: number]: Serie[] }
  ): Promise<void> {
    const html = this.criarHTMLFichaTreino(treino, cliente, exercicios, series);
    this.abrirJanelaImpressao(html, `Ficha_Treino_${cliente.nome.replace(/\s+/g, '_')}`);
  }

  // --------------------------------------------------------------------------
  // RELATÓRIO DE EVOLUÇÃO TEMPORAL
  // --------------------------------------------------------------------------
  static async gerarRelatorioEvolucao(
    cliente: Cliente,
    avaliacoes: AvaliacaoFisica[]
  ): Promise<void> {
    const html = this.criarHTMLRelatorioEvolucao(cliente, avaliacoes);
    this.abrirJanelaImpressao(html, `Relatorio_Evolucao_${cliente.nome.replace(/\s+/g, '_')}`);
  }

  // --------------------------------------------------------------------------
  // MÉTODOS AUXILIARES PARA CRIAÇÃO DO HTML
  // --------------------------------------------------------------------------
  
  private static criarHTMLAvaliacaoFisica(avaliacao: AvaliacaoFisica, cliente: Cliente): string {
    const idade = new Date().getFullYear() - new Date(cliente.data_nascimento).getFullYear();
    const imc = avaliacao.peso / Math.pow(avaliacao.altura / 100, 2);
    
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Relatório de Avaliação Física - ${cliente.nome}</title>
          <style>${this.PRINT_STYLES}</style>
        </head>
        <body>
          <div class="print-container">
            <div class="print-header">
              <div class="print-title">RELATÓRIO DE AVALIAÇÃO FÍSICA</div>
              <div class="print-subtitle">Personal Trainer System</div>
            </div>

            <div class="print-section">
              <div class="print-section-title">DADOS DO CLIENTE</div>
              <div class="print-info-grid">
                <div>
                  <div class="print-info-item">
                    <span class="print-info-label">Nome:</span> ${cliente.nome}
                  </div>
                  <div class="print-info-item">
                    <span class="print-info-label">Idade:</span> ${idade} anos
                  </div>
                  <div class="print-info-item">
                    <span class="print-info-label">Sexo:</span> ${cliente.sexo === 'M' ? 'Masculino' : 'Feminino'}
                  </div>
                </div>
                <div>
                  ${cliente.email ? `<div class="print-info-item"><span class="print-info-label">Email:</span> ${cliente.email}</div>` : ''}
                  ${cliente.telefone ? `<div class="print-info-item"><span class="print-info-label">Telefone:</span> ${cliente.telefone}</div>` : ''}
                  <div class="print-info-item">
                    <span class="print-info-label">Data da Avaliação:</span> ${formatarData(avaliacao.data_avaliacao)}
                  </div>
                </div>
              </div>
            </div>

            <div class="print-section">
              <div class="print-section-title">DADOS ANTROPOMÉTRICOS</div>
              <table class="print-table">
                <tr>
                  <th>Medida</th>
                  <th>Valor</th>
                  <th>Classificação</th>
                </tr>
                <tr>
                  <td>Peso</td>
                  <td>${avaliacao.peso} kg</td>
                  <td>-</td>
                </tr>
                <tr>
                  <td>Altura</td>
                  <td>${avaliacao.altura} cm</td>
                  <td>-</td>
                </tr>
                <tr>
                  <td>IMC</td>
                  <td>${imc.toFixed(2)} kg/m²</td>
                  <td>${this.classificarIMC(imc)}</td>
                </tr>
              </table>
            </div>

            ${avaliacao.dobras_cutaneas ? this.criarSecaoDobrasCutaneas(avaliacao.dobras_cutaneas) : ''}
            ${avaliacao.medidas_antropometricas ? this.criarSecaoMedidasAntropometricas(avaliacao.medidas_antropometricas) : ''}

            <div class="print-footer">
              Relatório gerado em ${new Date().toLocaleDateString('pt-BR')} - Personal Trainer System
            </div>
          </div>
        </body>
      </html>
    `;
  }

  private static criarHTMLFichaTreino(
    treino: Treino, 
    cliente: Cliente, 
    exercicios: Exercicio[],
    series: { [exercicioId: number]: Serie[] }
  ): string {
    const tiposTreino = ['A', 'B', 'C', 'D', 'E', 'F'];
    let treinosHTML = '';

    for (const tipo of tiposTreino) {
      const exerciciosTipo = exercicios.filter(ex => ex.tipo_treino === tipo);
      if (exerciciosTipo.length > 0) {
        treinosHTML += this.criarSecaoTipoTreino(tipo, exerciciosTipo, series);
      }
    }

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Ficha de Treino - ${cliente.nome}</title>
          <style>${this.PRINT_STYLES}</style>
        </head>
        <body>
          <div class="print-container">
            <div class="print-header">
              <div class="print-title">FICHA DE TREINO</div>
              <div class="print-subtitle">Personal Trainer System</div>
            </div>

            <div class="print-section">
              <div class="print-section-title">INFORMAÇÕES DO TREINO</div>
              <div class="print-info-grid">
                <div>
                  <div class="print-info-item">
                    <span class="print-info-label">Cliente:</span> ${cliente.nome}
                  </div>
                  <div class="print-info-item">
                    <span class="print-info-label">Nome do Treino:</span> ${treino.nome}
                  </div>
                </div>
                <div>
                  <div class="print-info-item">
                    <span class="print-info-label">Data de Início:</span> ${formatarData(treino.data_inicio)}
                  </div>
                  ${treino.data_fim ? `<div class="print-info-item"><span class="print-info-label">Data de Fim:</span> ${formatarData(treino.data_fim)}</div>` : ''}
                </div>
              </div>
              ${treino.observacoes ? `<div class="print-info-item"><span class="print-info-label">Observações:</span> ${treino.observacoes}</div>` : ''}
            </div>

            ${treinosHTML}

            <div class="print-footer">
              Ficha gerada em ${new Date().toLocaleDateString('pt-BR')} - Personal Trainer System
            </div>
          </div>
        </body>
      </html>
    `;
  }

  private static criarHTMLRelatorioEvolucao(cliente: Cliente, avaliacoes: AvaliacaoFisica[]): string {
    if (avaliacoes.length === 0) return '';

    const primeira = avaliacoes[0];
    const ultima = avaliacoes[avaliacoes.length - 1];
    
    let tabelaHTML = '';
    avaliacoes.forEach(avaliacao => {
      const imc = avaliacao.peso / Math.pow(avaliacao.altura / 100, 2);
      const percentualGordura = avaliacao.dobras_cutaneas?.percentual_gordura || 0;
      
      tabelaHTML += `
        <tr>
          <td>${formatarData(avaliacao.data_avaliacao)}</td>
          <td>${avaliacao.peso} kg</td>
          <td>${percentualGordura.toFixed(1)}%</td>
          <td>${imc.toFixed(1)}</td>
        </tr>
      `;
    });

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Relatório de Evolução - ${cliente.nome}</title>
          <style>${this.PRINT_STYLES}</style>
        </head>
        <body>
          <div class="print-container">
            <div class="print-header">
              <div class="print-title">RELATÓRIO DE EVOLUÇÃO</div>
              <div class="print-subtitle">Personal Trainer System</div>
            </div>

            <div class="print-section">
              <div class="print-section-title">DADOS DO CLIENTE</div>
              <div class="print-info-item">
                <span class="print-info-label">Nome:</span> ${cliente.nome}
              </div>
              <div class="print-info-item">
                <span class="print-info-label">Período:</span> ${formatarData(primeira.data_avaliacao)} a ${formatarData(ultima.data_avaliacao)}
              </div>
              <div class="print-info-item">
                <span class="print-info-label">Total de Avaliações:</span> ${avaliacoes.length}
              </div>
            </div>

            <div class="print-section">
              <div class="print-section-title">TABELA COMPARATIVA</div>
              <table class="print-table">
                <thead>
                  <tr>
                    <th>Data</th>
                    <th>Peso</th>
                    <th>% Gordura</th>
                    <th>IMC</th>
                  </tr>
                </thead>
                <tbody>
                  ${tabelaHTML}
                </tbody>
              </table>
            </div>

            <div class="print-footer">
              Relatório gerado em ${new Date().toLocaleDateString('pt-BR')} - Personal Trainer System
            </div>
          </div>
        </body>
      </html>
    `;
  }

  // --------------------------------------------------------------------------
  // MÉTODOS AUXILIARES
  // --------------------------------------------------------------------------

  private static criarSecaoDobrasCutaneas(dobras: any): string {
    const somadobras = dobras.peitoral + dobras.tricipital + dobras.bicipital +
                      dobras.axilar_media + dobras.suprailiaca + dobras.abdominal + dobras.coxa;

    return `
      <div class="print-section">
        <div class="print-section-title">DOBRAS CUTÂNEAS (mm)</div>
        <table class="print-table">
          <tr>
            <td><strong>Peitoral:</strong> ${dobras.peitoral} mm</td>
            <td><strong>Trícipital:</strong> ${dobras.tricipital} mm</td>
          </tr>
          <tr>
            <td><strong>Bicipital:</strong> ${dobras.bicipital} mm</td>
            <td><strong>Axilar Média:</strong> ${dobras.axilar_media} mm</td>
          </tr>
          <tr>
            <td><strong>Supra-ilíaca:</strong> ${dobras.suprailiaca} mm</td>
            <td><strong>Abdominal:</strong> ${dobras.abdominal} mm</td>
          </tr>
          <tr>
            <td><strong>Coxa:</strong> ${dobras.coxa} mm</td>
            <td><strong>Panturrilha:</strong> ${dobras.panturrilha} mm</td>
          </tr>
          <tr>
            <td colspan="2"><strong>Soma das 7 dobras:</strong> ${somadobras.toFixed(2)} mm</td>
          </tr>
          ${dobras.percentual_gordura ? `<tr><td colspan="2"><strong>Percentual de Gordura:</strong> ${dobras.percentual_gordura.toFixed(2)}%</td></tr>` : ''}
        </table>
      </div>
    `;
  }

  private static criarSecaoMedidasAntropometricas(medidas: any): string {
    return `
      <div class="print-section">
        <div class="print-section-title">MEDIDAS ANTROPOMÉTRICAS (cm)</div>
        <table class="print-table">
          <tr>
            <td><strong>Braço Direito:</strong> ${medidas.braco_direito} cm</td>
            <td><strong>Braço Esquerdo:</strong> ${medidas.braco_esquerdo} cm</td>
          </tr>
          <tr>
            <td><strong>Antebraço Direito:</strong> ${medidas.antebraco_direito} cm</td>
            <td><strong>Antebraço Esquerdo:</strong> ${medidas.antebraco_esquerdo} cm</td>
          </tr>
          <tr>
            <td><strong>Peitoral:</strong> ${medidas.peitoral} cm</td>
            <td><strong>Cintura:</strong> ${medidas.cintura} cm</td>
          </tr>
          <tr>
            <td><strong>Abdômen:</strong> ${medidas.abdomen} cm</td>
            <td><strong>Quadril:</strong> ${medidas.quadril} cm</td>
          </tr>
          <tr>
            <td><strong>Coxa Direita:</strong> ${medidas.coxa_direita} cm</td>
            <td><strong>Coxa Esquerda:</strong> ${medidas.coxa_esquerda} cm</td>
          </tr>
          <tr>
            <td><strong>Panturrilha Direita:</strong> ${medidas.panturrilha_direita} cm</td>
            <td><strong>Panturrilha Esquerda:</strong> ${medidas.panturrilha_esquerda} cm</td>
          </tr>
        </table>
      </div>
    `;
  }

  private static criarSecaoTipoTreino(
    tipo: string,
    exercicios: Exercicio[],
    series: { [exercicioId: number]: Serie[] }
  ): string {
    let exerciciosHTML = '';

    exercicios.forEach((exercicio, index) => {
      const exercicioSeries = series[exercicio.id!] || [];
      let seriesHTML = '';

      if (exercicioSeries.length > 0) {
        exercicioSeries.forEach(serie => {
          seriesHTML += `
            <tr>
              <td>${serie.semana}</td>
              <td>${serie.numero_serie}</td>
              <td>${serie.repeticoes}</td>
              <td>${serie.carga} kg</td>
              <td>${serie.volume_carga}</td>
            </tr>
          `;
        });
      }

      exerciciosHTML += `
        <div class="print-section">
          <h4>${index + 1}. ${exercicio.nome}</h4>
          ${seriesHTML ? `
            <table class="print-table">
              <thead>
                <tr>
                  <th>Semana</th>
                  <th>Série</th>
                  <th>Repetições</th>
                  <th>Carga (kg)</th>
                  <th>Volume</th>
                </tr>
              </thead>
              <tbody>
                ${seriesHTML}
              </tbody>
            </table>
          ` : ''}
        </div>
      `;
    });

    return `
      <div class="page-break">
        <div class="print-section">
          <div class="print-section-title">TREINO ${tipo}</div>
          ${exerciciosHTML}
        </div>
      </div>
    `;
  }

  private static classificarIMC(imc: number): string {
    if (imc < 18.5) return 'Abaixo do peso';
    if (imc < 25) return 'Peso normal';
    if (imc < 30) return 'Sobrepeso';
    if (imc < 35) return 'Obesidade grau I';
    if (imc < 40) return 'Obesidade grau II';
    return 'Obesidade grau III';
  }

  private static abrirJanelaImpressao(html: string, filename: string): void {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(html);
      printWindow.document.close();

      // Aguardar o carregamento e imprimir
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
        }, 500);
      };
    }
  }

  // --------------------------------------------------------------------------
  // MÉTODO PARA PREVIEW EM TELA
  // --------------------------------------------------------------------------

  static criarPreviewHTML(html: string): string {
    return html.replace('print-container', 'print-container print-preview');
  }
}
