import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// ============================================================================
// SETTINGS CONTEXT - GERENCIAMENTO DE CONFIGURAÇÕES DO USUÁRIO
// ============================================================================

export interface UserSettings {
  // Aparência
  theme: 'light' | 'dark' | 'auto';
  primaryColor: string;
  fontSize: 'small' | 'medium' | 'large';
  compactMode: boolean;
  
  // Funcionalidades
  autoSave: boolean;
  autoBackup: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  showAnimations: boolean;
  soundEffects: boolean;
  
  // Notificações
  notifications: boolean;
  emailNotifications: boolean;
  reminderNotifications: boolean;
  
  // Dados e Privacidade
  dataRetention: number; // dias
  analyticsEnabled: boolean;
  crashReporting: boolean;
  
  // Interface
  sidebarCollapsed: boolean;
  defaultPage: string;
  itemsPerPage: number;
  showTutorials: boolean;
  
  // Backup
  lastBackupDate?: string;
  autoBackupLocation: string;
  
  // Personalização
  userName: string;
  userEmail: string;
  clinicName: string;
  clinicAddress: string;
  clinicPhone: string;
  clinicLogo?: string;
}

const defaultSettings: UserSettings = {
  // Aparência
  theme: 'light',
  primaryColor: '#FF6B35',
  fontSize: 'medium',
  compactMode: true,
  
  // Funcionalidades
  autoSave: true,
  autoBackup: false,
  backupFrequency: 'weekly',
  showAnimations: true,
  soundEffects: false,
  
  // Notificações
  notifications: true,
  emailNotifications: false,
  reminderNotifications: true,
  
  // Dados e Privacidade
  dataRetention: 365,
  analyticsEnabled: true,
  crashReporting: true,
  
  // Interface
  sidebarCollapsed: false,
  defaultPage: '/',
  itemsPerPage: 10,
  showTutorials: true,
  
  // Backup
  autoBackupLocation: 'local',
  
  // Personalização
  userName: '',
  userEmail: '',
  clinicName: 'Personal Trainer',
  clinicAddress: '',
  clinicPhone: ''
};

interface SettingsContextType {
  settings: UserSettings;
  updateSettings: (newSettings: Partial<UserSettings>) => void;
  resetSettings: () => void;
  exportSettings: () => string;
  importSettings: (settingsJson: string) => boolean;
  isDarkMode: boolean;
  toggleTheme: () => void;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

interface SettingsProviderProps {
  children: ReactNode;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<UserSettings>(defaultSettings);

  // Carregar configurações do localStorage na inicialização
  useEffect(() => {
    const savedSettings = localStorage.getItem('userSettings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings({ ...defaultSettings, ...parsedSettings });
      } catch (error) {
        console.error('Erro ao carregar configurações:', error);
      }
    }
  }, []);

  // Salvar configurações no localStorage sempre que mudarem
  useEffect(() => {
    localStorage.setItem('userSettings', JSON.stringify(settings));
  }, [settings]);

  // Aplicar tema automaticamente
  useEffect(() => {
    const applyTheme = () => {
      const root = document.documentElement;
      
      if (settings.theme === 'dark' || 
          (settings.theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        root.setAttribute('data-theme', 'dark');
      } else {
        root.setAttribute('data-theme', 'light');
      }
    };

    applyTheme();

    // Escutar mudanças no tema do sistema
    if (settings.theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', applyTheme);
      
      return () => mediaQuery.removeEventListener('change', applyTheme);
    }
  }, [settings.theme]);

  const updateSettings = (newSettings: Partial<UserSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
    localStorage.removeItem('userSettings');
  };

  const exportSettings = (): string => {
    return JSON.stringify(settings, null, 2);
  };

  const importSettings = (settingsJson: string): boolean => {
    try {
      const importedSettings = JSON.parse(settingsJson);
      
      // Validar estrutura básica
      if (typeof importedSettings === 'object' && importedSettings !== null) {
        setSettings({ ...defaultSettings, ...importedSettings });
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Erro ao importar configurações:', error);
      return false;
    }
  };

  const toggleTheme = () => {
    const newTheme = settings.theme === 'light' ? 'dark' : 'light';
    updateSettings({ theme: newTheme });
  };

  const isDarkMode = settings.theme === 'dark' || 
    (settings.theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

  const value: SettingsContextType = {
    settings,
    updateSettings,
    resetSettings,
    exportSettings,
    importSettings,
    isDarkMode,
    toggleTheme
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};

export const useSettings = (): SettingsContextType => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings deve ser usado dentro de um SettingsProvider');
  }
  return context;
};

// Hook para configurações específicas
export const useTheme = () => {
  const { settings, isDarkMode, toggleTheme } = useSettings();
  return {
    theme: settings.theme,
    isDarkMode,
    toggleTheme,
    primaryColor: settings.primaryColor,
    fontSize: settings.fontSize,
    compactMode: settings.compactMode,
    showAnimations: settings.showAnimations
  };
};

export const useNotifications = () => {
  const { settings, updateSettings } = useSettings();
  return {
    notifications: settings.notifications,
    emailNotifications: settings.emailNotifications,
    reminderNotifications: settings.reminderNotifications,
    updateNotifications: (notifications: Partial<Pick<UserSettings, 'notifications' | 'emailNotifications' | 'reminderNotifications'>>) => {
      updateSettings(notifications);
    }
  };
};

export const useBackupSettings = () => {
  const { settings, updateSettings } = useSettings();
  return {
    autoBackup: settings.autoBackup,
    backupFrequency: settings.backupFrequency,
    lastBackupDate: settings.lastBackupDate,
    autoBackupLocation: settings.autoBackupLocation,
    updateBackupSettings: (backupSettings: Partial<Pick<UserSettings, 'autoBackup' | 'backupFrequency' | 'lastBackupDate' | 'autoBackupLocation'>>) => {
      updateSettings(backupSettings);
    }
  };
};

export const useClinicInfo = () => {
  const { settings, updateSettings } = useSettings();
  return {
    clinicName: settings.clinicName,
    clinicAddress: settings.clinicAddress,
    clinicPhone: settings.clinicPhone,
    clinicLogo: settings.clinicLogo,
    userName: settings.userName,
    userEmail: settings.userEmail,
    updateClinicInfo: (clinicInfo: Partial<Pick<UserSettings, 'clinicName' | 'clinicAddress' | 'clinicPhone' | 'clinicLogo' | 'userName' | 'userEmail'>>) => {
      updateSettings(clinicInfo);
    }
  };
};

export default SettingsContext;
