import db from '../db/db';

// ============================================================================
// ANALYTICS SERVICE - SISTEMA DE ANALYTICS E MÉTRICAS
// ============================================================================

export interface AnalyticsEvent {
  id?: number;
  event_type: string;
  event_data: string; // JSON string
  timestamp: string;
  user_session?: string;
  page_url?: string;
}

export interface DashboardMetrics {
  totalClientes: number;
  totalAvaliacoes: number;
  totalTreinos: number;
  clientesAtivos: number;
  avaliacoesEstesMes: number;
  treinosEstesMes: number;
  crescimentoClientes: number; // percentual
  crescimentoAvaliacoes: number; // percentual
}

export interface UsageMetrics {
  pageViews: Record<string, number>;
  featureUsage: Record<string, number>;
  sessionDuration: number;
  totalSessions: number;
  averageSessionDuration: number;
}

export interface ClientMetrics {
  clientesPorSexo: { masculino: number; feminino: number };
  clientesPorIdade: Record<string, number>;
  avaliacoesPorMes: Record<string, number>;
  treinosPorMes: Record<string, number>;
  clientesMaisAtivos: Array<{ nome: string; atividades: number }>;
}

export class AnalyticsService {
  private static sessionId: string = this.generateSessionId();
  private static sessionStart: Date = new Date();

  // --------------------------------------------------------------------------
  // INICIALIZAÇÃO E CONFIGURAÇÃO
  // --------------------------------------------------------------------------

  static initialize() {
    this.createAnalyticsTable();
    this.trackEvent('app_start', { timestamp: new Date().toISOString() });
    
    // Rastrear tempo de sessão
    window.addEventListener('beforeunload', () => {
      this.trackSessionEnd();
    });

    // Rastrear mudanças de página
    this.trackPageView(window.location.pathname);
  }

  private static createAnalyticsTable() {
    try {
      db.exec(`
        CREATE TABLE IF NOT EXISTS analytics_events (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          event_type TEXT NOT NULL,
          event_data TEXT,
          timestamp TEXT NOT NULL,
          user_session TEXT,
          page_url TEXT
        )
      `);
    } catch (error) {
      console.error('Erro ao criar tabela de analytics:', error);
    }
  }

  private static generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // --------------------------------------------------------------------------
  // RASTREAMENTO DE EVENTOS
  // --------------------------------------------------------------------------

  static trackEvent(eventType: string, eventData: any = {}) {
    try {
      const event: AnalyticsEvent = {
        event_type: eventType,
        event_data: JSON.stringify(eventData),
        timestamp: new Date().toISOString(),
        user_session: this.sessionId,
        page_url: window.location.pathname
      };

      const stmt = db.prepare(`
        INSERT INTO analytics_events (event_type, event_data, timestamp, user_session, page_url)
        VALUES (?, ?, ?, ?, ?)
      `);

      stmt.run(
        event.event_type,
        event.event_data,
        event.timestamp,
        event.user_session,
        event.page_url
      );
    } catch (error) {
      console.error('Erro ao rastrear evento:', error);
    }
  }

  static trackPageView(path: string) {
    this.trackEvent('page_view', { path });
  }

  static trackFeatureUsage(feature: string, details: any = {}) {
    this.trackEvent('feature_usage', { feature, ...details });
  }

  static trackUserAction(action: string, details: any = {}) {
    this.trackEvent('user_action', { action, ...details });
  }

  static trackError(error: string, details: any = {}) {
    this.trackEvent('error', { error, ...details });
  }

  static trackPerformance(metric: string, value: number, details: any = {}) {
    this.trackEvent('performance', { metric, value, ...details });
  }

  private static trackSessionEnd() {
    const sessionDuration = new Date().getTime() - this.sessionStart.getTime();
    this.trackEvent('session_end', { 
      duration: sessionDuration,
      sessionId: this.sessionId 
    });
  }

  // --------------------------------------------------------------------------
  // MÉTRICAS DO DASHBOARD
  // --------------------------------------------------------------------------

  static getDashboardMetrics(): DashboardMetrics {
    try {
      const now = new Date();
      const thisMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const lastMonthStr = `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`;

      // Total de registros
      const totalClientes = db.prepare('SELECT COUNT(*) as count FROM clientes').get() as { count: number };
      const totalAvaliacoes = db.prepare('SELECT COUNT(*) as count FROM avaliacoes_fisicas').get() as { count: number };
      const totalTreinos = db.prepare('SELECT COUNT(*) as count FROM treinos').get() as { count: number };

      // Clientes ativos (com atividade nos últimos 30 dias)
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
      const clientesAtivos = db.prepare(`
        SELECT COUNT(DISTINCT cliente_id) as count 
        FROM avaliacoes_fisicas 
        WHERE data_avaliacao >= ?
      `).get(thirtyDaysAgo) as { count: number };

      // Atividades este mês
      const avaliacoesEstesMes = db.prepare(`
        SELECT COUNT(*) as count 
        FROM avaliacoes_fisicas 
        WHERE strftime('%Y-%m', data_avaliacao) = ?
      `).get(thisMonth) as { count: number };

      const treinosEstesMes = db.prepare(`
        SELECT COUNT(*) as count 
        FROM treinos 
        WHERE strftime('%Y-%m', data_criacao) = ?
      `).get(thisMonth) as { count: number };

      // Crescimento (comparação com mês anterior)
      const clientesUltimoMes = db.prepare(`
        SELECT COUNT(*) as count 
        FROM clientes 
        WHERE strftime('%Y-%m', data_cadastro) = ?
      `).get(lastMonthStr) as { count: number };

      const avaliacoesUltimoMes = db.prepare(`
        SELECT COUNT(*) as count 
        FROM avaliacoes_fisicas 
        WHERE strftime('%Y-%m', data_avaliacao) = ?
      `).get(lastMonthStr) as { count: number };

      const crescimentoClientes = clientesUltimoMes.count > 0 
        ? ((totalClientes.count - clientesUltimoMes.count) / clientesUltimoMes.count) * 100 
        : 0;

      const crescimentoAvaliacoes = avaliacoesUltimoMes.count > 0 
        ? ((avaliacoesEstesMes.count - avaliacoesUltimoMes.count) / avaliacoesUltimoMes.count) * 100 
        : 0;

      return {
        totalClientes: totalClientes.count,
        totalAvaliacoes: totalAvaliacoes.count,
        totalTreinos: totalTreinos.count,
        clientesAtivos: clientesAtivos.count,
        avaliacoesEstesMes: avaliacoesEstesMes.count,
        treinosEstesMes: treinosEstesMes.count,
        crescimentoClientes: Math.round(crescimentoClientes * 100) / 100,
        crescimentoAvaliacoes: Math.round(crescimentoAvaliacoes * 100) / 100
      };
    } catch (error) {
      console.error('Erro ao obter métricas do dashboard:', error);
      return {
        totalClientes: 0,
        totalAvaliacoes: 0,
        totalTreinos: 0,
        clientesAtivos: 0,
        avaliacoesEstesMes: 0,
        treinosEstesMes: 0,
        crescimentoClientes: 0,
        crescimentoAvaliacoes: 0
      };
    }
  }

  // --------------------------------------------------------------------------
  // MÉTRICAS DE USO
  // --------------------------------------------------------------------------

  static getUsageMetrics(): UsageMetrics {
    try {
      // Page views
      const pageViewsResult = db.prepare(`
        SELECT page_url, COUNT(*) as count 
        FROM analytics_events 
        WHERE event_type = 'page_view' 
        GROUP BY page_url
      `).all() as Array<{ page_url: string; count: number }>;

      const pageViews: Record<string, number> = {};
      pageViewsResult.forEach(row => {
        pageViews[row.page_url] = row.count;
      });

      // Feature usage
      const featureUsageResult = db.prepare(`
        SELECT JSON_EXTRACT(event_data, '$.feature') as feature, COUNT(*) as count 
        FROM analytics_events 
        WHERE event_type = 'feature_usage' 
        GROUP BY feature
      `).all() as Array<{ feature: string; count: number }>;

      const featureUsage: Record<string, number> = {};
      featureUsageResult.forEach(row => {
        if (row.feature) {
          featureUsage[row.feature] = row.count;
        }
      });

      // Session metrics
      const sessionMetrics = db.prepare(`
        SELECT 
          COUNT(DISTINCT user_session) as totalSessions,
          AVG(CAST(JSON_EXTRACT(event_data, '$.duration') AS INTEGER)) as avgDuration
        FROM analytics_events 
        WHERE event_type = 'session_end'
      `).get() as { totalSessions: number; avgDuration: number };

      const currentSessionDuration = new Date().getTime() - this.sessionStart.getTime();

      return {
        pageViews,
        featureUsage,
        sessionDuration: currentSessionDuration,
        totalSessions: sessionMetrics.totalSessions || 0,
        averageSessionDuration: sessionMetrics.avgDuration || 0
      };
    } catch (error) {
      console.error('Erro ao obter métricas de uso:', error);
      return {
        pageViews: {},
        featureUsage: {},
        sessionDuration: 0,
        totalSessions: 0,
        averageSessionDuration: 0
      };
    }
  }

  // --------------------------------------------------------------------------
  // MÉTRICAS DE CLIENTES
  // --------------------------------------------------------------------------

  static getClientMetrics(): ClientMetrics {
    try {
      // Clientes por sexo
      const sexoResult = db.prepare(`
        SELECT sexo, COUNT(*) as count 
        FROM clientes 
        GROUP BY sexo
      `).all() as Array<{ sexo: string; count: number }>;

      const clientesPorSexo = { masculino: 0, feminino: 0 };
      sexoResult.forEach(row => {
        if (row.sexo === 'M') clientesPorSexo.masculino = row.count;
        if (row.sexo === 'F') clientesPorSexo.feminino = row.count;
      });

      // Clientes por faixa etária (implementação simplificada)
      const clientesPorIdade: Record<string, number> = {
        '18-25': 0,
        '26-35': 0,
        '36-45': 0,
        '46-55': 0,
        '56+': 0
      };

      // Avaliações por mês (últimos 6 meses)
      const avaliacoesPorMes: Record<string, number> = {};
      for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        
        const count = db.prepare(`
          SELECT COUNT(*) as count 
          FROM avaliacoes_fisicas 
          WHERE strftime('%Y-%m', data_avaliacao) = ?
        `).get(monthKey) as { count: number };
        
        avaliacoesPorMes[monthKey] = count.count;
      }

      // Treinos por mês (últimos 6 meses)
      const treinosPorMes: Record<string, number> = {};
      for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        
        const count = db.prepare(`
          SELECT COUNT(*) as count 
          FROM treinos 
          WHERE strftime('%Y-%m', data_criacao) = ?
        `).get(monthKey) as { count: number };
        
        treinosPorMes[monthKey] = count.count;
      }

      // Clientes mais ativos (por número de avaliações)
      const clientesMaisAtivos = db.prepare(`
        SELECT c.nome, COUNT(a.id) as atividades
        FROM clientes c
        LEFT JOIN avaliacoes_fisicas a ON c.id = a.cliente_id
        GROUP BY c.id, c.nome
        ORDER BY atividades DESC
        LIMIT 5
      `).all() as Array<{ nome: string; atividades: number }>;

      return {
        clientesPorSexo,
        clientesPorIdade,
        avaliacoesPorMes,
        treinosPorMes,
        clientesMaisAtivos
      };
    } catch (error) {
      console.error('Erro ao obter métricas de clientes:', error);
      return {
        clientesPorSexo: { masculino: 0, feminino: 0 },
        clientesPorIdade: {},
        avaliacoesPorMes: {},
        treinosPorMes: {},
        clientesMaisAtivos: []
      };
    }
  }

  // --------------------------------------------------------------------------
  // LIMPEZA DE DADOS
  // --------------------------------------------------------------------------

  static cleanOldAnalytics(daysToKeep: number = 90) {
    try {
      const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000).toISOString();
      
      const stmt = db.prepare('DELETE FROM analytics_events WHERE timestamp < ?');
      const result = stmt.run(cutoffDate);
      
      console.log(`Removidos ${result.changes} eventos de analytics antigos`);
    } catch (error) {
      console.error('Erro ao limpar analytics antigos:', error);
    }
  }
}

export default AnalyticsService;
