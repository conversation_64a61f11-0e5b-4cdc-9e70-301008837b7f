# 📦 Guia de Instalação - KHATFIT

## 🚨 Problema Conhecido com NPM

**IMPORTANTE**: Este projeto apresenta problemas de compatibilidade com `npm install`. Use **YARN** como gerenciador de pacotes.

### ❌ Problema com NPM
```bash
npm install  # ❌ NÃO FUNCIONA - Conflitos de dependências
```

### ✅ Solução: Use YARN
```bash
yarn install  # ✅ FUNCIONA PERFEITAMENTE
```

---

## 🔧 Pré-requisitos

### Sistema Operacional
- **Windows**: 10 ou superior
- **macOS**: 10.13 ou superior  
- **Linux**: Distribuições modernas (Ubuntu 18+, etc.)

### Software Necessário
- **Node.js**: Versão 16.x ou superior
- **Yarn**: Versão 1.22.x ou superior
- **Git**: Para clonar o repositório

### Verificar Versões
```bash
node --version    # Deve ser v16.x.x ou superior
yarn --version    # Deve ser 1.22.x ou superior
git --version     # Qualquer versão recente
```

---

## 📥 Instalação Passo a Passo

### 1. Clonar o Repositório
```bash
git clone https://github.com/seu-usuario/khatfit-app.git
cd khatfit-app
```

### 2. Instalar Dependências (USAR YARN)
```bash
# ✅ MÉTODO CORRETO
yarn install

# ❌ NÃO USE NPM
# npm install  # Vai dar erro!
```

### 3. Verificar Instalação
```bash
# Verificar se as dependências foram instaladas
ls node_modules/  # Deve mostrar muitas pastas

# Verificar scripts disponíveis
yarn run  # Lista todos os scripts disponíveis
```

---

## 🚀 Executar a Aplicação

### Desenvolvimento (React + Electron)
```bash
# Inicia o servidor React e abre o Electron
yarn dev

# OU usar o script batch (Windows)
start.bat

# OU usar o script shell (Linux/Mac)
./start.sh
```

### Apenas React (Navegador)
```bash
# Apenas o servidor de desenvolvimento React
yarn start

# Acesse: http://localhost:3000
```

### Build de Produção
```bash
# Gerar build otimizado
yarn build

# Gerar executável Electron
yarn dist
```

---

## 🔍 Troubleshooting

### Problema: "yarn: command not found"
```bash
# Instalar Yarn globalmente
npm install -g yarn

# OU usando Chocolatey (Windows)
choco install yarn

# OU usando Homebrew (Mac)
brew install yarn
```

### Problema: Erro de permissões (Linux/Mac)
```bash
# Dar permissão aos scripts
chmod +x start.sh

# OU executar com sudo se necessário
sudo yarn install
```

### Problema: Porta 3000 ocupada
```bash
# Matar processo na porta 3000
# Windows
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# Linux/Mac
lsof -ti:3000 | xargs kill -9
```

### Problema: Electron não abre
```bash
# Limpar cache e reinstalar
rm -rf node_modules/
rm yarn.lock
yarn install
yarn dev
```

---

## 📁 Estrutura de Scripts

### Scripts Disponíveis
```json
{
  "start": "react-app-rewired start",           // Servidor React
  "build": "react-app-rewired build",           // Build produção
  "test": "react-app-rewired test",             // Testes
  "dev": "concurrently \"yarn start\" \"yarn electron-dev\"",  // React + Electron
  "electron-dev": "wait-on http://localhost:3000 && electron .", // Electron dev
  "pack": "electron-builder --dir",             // Build Electron (pasta)
  "dist": "electron-builder"                    // Build Electron (instalador)
}
```

### Comandos Úteis
```bash
# Desenvolvimento completo
yarn dev

# Apenas React (navegador)
yarn start

# Testes
yarn test

# Build para produção
yarn build

# Gerar executável
yarn dist

# Limpar e reinstalar
yarn clean-install  # (se disponível)
```

---

## 🐛 Problemas Conhecidos e Soluções

### 1. NPM vs YARN
- **Problema**: Conflitos de dependências com npm
- **Solução**: Sempre usar yarn
- **Motivo**: Electron e React têm dependências complexas

### 2. Better-SQLite3 (Banco de Dados)
- **Problema**: Pode falhar em alguns sistemas
- **Solução**: Sistema tem fallback automático para mock
- **Verificação**: Aplicação funciona mesmo se SQLite falhar

### 3. Electron em Desenvolvimento
- **Problema**: Electron pode não abrir automaticamente
- **Solução**: Aguardar React carregar completamente (wait-on)
- **Alternativa**: Abrir http://localhost:3000 no navegador

### 4. Dependências Nativas
- **Problema**: better-sqlite3 precisa compilar código nativo
- **Solução**: Yarn gerencia melhor dependências nativas
- **Windows**: Pode precisar do Visual Studio Build Tools

---

## ✅ Verificação de Sucesso

### Aplicação Funcionando Corretamente
1. **React Server**: http://localhost:3000 carrega
2. **Electron Window**: Janela desktop abre automaticamente
3. **Database**: Dados persistem entre sessões
4. **Navigation**: Todas as páginas carregam (Clientes, Avaliações, Treinos)
5. **CRUD**: Consegue criar, editar e excluir registros

### Logs de Sucesso
```
✓ Compiled successfully!
✓ Local:            http://localhost:3000
✓ Electron app started
✓ Database initialized successfully
```

---

## 📞 Suporte

### Se ainda tiver problemas:
1. **Verificar versões** do Node.js e Yarn
2. **Limpar cache**: `yarn cache clean`
3. **Reinstalar**: Deletar `node_modules` e `yarn.lock`, depois `yarn install`
4. **Verificar logs**: Procurar erros específicos no terminal
5. **Testar apenas React**: `yarn start` para isolar problemas do Electron

### Logs Importantes
- Terminal mostra erros detalhados
- DevTools do Electron (F12) para erros de frontend
- Console do navegador em http://localhost:3000
