import React from 'react';
import { Box, Typography, Button, Container } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { colors } from '../styles/colors';

const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Container maxWidth="md" sx={{ py: { xs: 2, sm: 3 } }}>
      <Box sx={{
        mb: { xs: 2, sm: 3 },
        textAlign: 'center',
        background: `${colors.sea}0A`,
        borderRadius: 3,
        p: { xs: 2, sm: 3 }
      }}>
        <Typography
          variant="h3"
          gutterBottom
          fontWeight="bold"
          sx={{
            color: colors.ocean,
            fontSize: { xs: '1.5rem', sm: '2rem' }
          }}
        >
          Página Não Encontrada
        </Typography>
        <Typography
          variant="subtitle1"
          sx={{
            color: `${colors.ocean}99`,
            fontSize: { xs: '0.875rem', sm: '1rem' }
          }}
        >
          Ops! A página que você procura não existe
        </Typography>
      </Box>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '50vh',
          textAlign: 'center',
          p: { xs: 2, sm: 3 },
          bgcolor: colors.cloud,
          borderRadius: 3,
          boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)'
        }}
      >
        <Box sx={{
          display: 'inline-flex',
          p: 3,
          borderRadius: '50%',
          mb: 3,
          bgcolor: `${colors.sea}1A`
        }}>
          <ErrorOutlineIcon sx={{
            fontSize: 80,
            color: colors.sea
          }} />
        </Box>

        <Typography
          variant="h4"
          component="h1"
          gutterBottom
          sx={{
            color: colors.ocean,
            fontWeight: 600,
            fontSize: { xs: '1.5rem', sm: '2rem' }
          }}
        >
          404 - Página não encontrada
        </Typography>

        <Typography
          variant="body1"
          paragraph
          sx={{
            color: `${colors.ocean}99`,
            mb: 3,
            maxWidth: 400
          }}
        >
          A página que você está procurando não existe ou foi movida.
          Verifique o endereço ou volte para a página inicial.
        </Typography>

        <Button
          variant="contained"
          onClick={() => navigate('/')}
          sx={{
            bgcolor: colors.sea,
            color: colors.cloud,
            borderRadius: 2,
            py: 1.2,
            px: 3,
            '&:hover': {
              bgcolor: colors.ocean,
              boxShadow: `0px 4px 8px ${colors.sea}33`
            }
          }}
        >
          Voltar para a página inicial
        </Button>
      </Box>
    </Container>
  );
};

export default NotFoundPage; 