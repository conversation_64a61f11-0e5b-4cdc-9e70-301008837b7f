import React, { useState } from 'react';
import {
  Box,
  IconButton,
  Badge,
  Popover,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Button,
  Divider,
  Chip,
  Tooltip,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  NotificationsNone as NotificationsNoneIcon,
  Info as InfoIcon,
  CheckCircle as SuccessIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Schedule as ReminderIcon,
  MoreVert as MoreIcon,
  Clear as ClearIcon,
  DoneAll as DoneAllIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { useNotifications, AppNotification } from '../../contexts/NotificationContext';
import { colors } from '../../styles/colors';
import { AnimatedBox } from './AnimatedComponents';

// ============================================================================
// NOTIFICATION CENTER - CENTRO DE NOTIFICAÇÕES
// ============================================================================

interface NotificationCenterProps {
  onSettingsClick?: () => void;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({ onSettingsClick }) => {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications
  } = useNotifications();

  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [menuAnchorEl, setMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [selectedNotification, setSelectedNotification] = useState<string | null>(null);

  const isOpen = Boolean(anchorEl);
  const isMenuOpen = Boolean(menuAnchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setMenuAnchorEl(null);
    setSelectedNotification(null);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, notificationId: string) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
    setSelectedNotification(notificationId);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setSelectedNotification(null);
  };

  const handleNotificationClick = (notification: AppNotification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }

    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
      handleClose();
    }
  };

  const handleMarkAsRead = (notificationId: string) => {
    markAsRead(notificationId);
    handleMenuClose();
  };

  const handleRemove = (notificationId: string) => {
    removeNotification(notificationId);
    handleMenuClose();
  };

  const getNotificationIcon = (type: AppNotification['type']) => {
    const iconProps = { fontSize: 'small' as const };
    
    switch (type) {
      case 'success':
        return <SuccessIcon {...iconProps} sx={{ color: colors.health[500] }} />;
      case 'warning':
        return <WarningIcon {...iconProps} sx={{ color: colors.warning }} />;
      case 'error':
        return <ErrorIcon {...iconProps} sx={{ color: colors.error }} />;
      case 'reminder':
        return <ReminderIcon {...iconProps} sx={{ color: colors.energy[500] }} />;
      default:
        return <InfoIcon {...iconProps} sx={{ color: colors.info }} />;
    }
  };

  const getNotificationColor = (type: AppNotification['type']) => {
    switch (type) {
      case 'success': return colors.health[50];
      case 'warning': return '#FFF8E1';
      case 'error': return '#FFEBEE';
      case 'reminder': return colors.energy[50];
      default: return colors.professional[50];
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Agora';
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    if (days < 7) return `${days}d`;
    return timestamp.toLocaleDateString('pt-BR');
  };

  const recentNotifications = notifications.slice(0, 10);

  return (
    <>
      <Tooltip title="Notificações">
        <IconButton onClick={handleClick} size="small">
          <Badge badgeContent={unreadCount} color="error" max={99}>
            {unreadCount > 0 ? (
              <NotificationsIcon sx={{ color: colors.energy[500] }} />
            ) : (
              <NotificationsNoneIcon sx={{ color: colors.professional[600] }} />
            )}
          </Badge>
        </IconButton>
      </Tooltip>

      <Popover
        open={isOpen}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: {
            width: 400,
            maxHeight: 500,
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
            border: `1px solid ${colors.professional[200]}`
          }
        }}
      >
        <Box sx={{ p: 2, borderBottom: `1px solid ${colors.professional[200]}` }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6" sx={{ fontWeight: 600, color: colors.professional[700] }}>
              Notificações
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1 }}>
              {unreadCount > 0 && (
                <Tooltip title="Marcar todas como lidas">
                  <IconButton size="small" onClick={markAllAsRead}>
                    <DoneAllIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              
              <Tooltip title="Configurações de notificação">
                <IconButton size="small" onClick={onSettingsClick}>
                  <SettingsIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              
              {notifications.length > 0 && (
                <Tooltip title="Limpar todas">
                  <IconButton size="small" onClick={clearAllNotifications}>
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          </Box>
          
          {unreadCount > 0 && (
            <Typography variant="body2" sx={{ color: colors.professional[600], mt: 0.5 }}>
              {unreadCount} não {unreadCount === 1 ? 'lida' : 'lidas'}
            </Typography>
          )}
        </Box>

        {recentNotifications.length === 0 ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <NotificationsNoneIcon sx={{ fontSize: 48, color: colors.professional[300], mb: 2 }} />
            <Typography variant="body2" sx={{ color: colors.professional[500] }}>
              Nenhuma notificação
            </Typography>
            <Typography variant="caption" sx={{ color: colors.professional[400] }}>
              Você está em dia com tudo!
            </Typography>
          </Box>
        ) : (
          <List sx={{ py: 0, maxHeight: 350, overflow: 'auto' }}>
            {recentNotifications.map((notification, index) => (
              <AnimatedBox key={notification.id} animation="fadeInUp" delay={index * 0.05}>
                <ListItem
                  button
                  onClick={() => handleNotificationClick(notification)}
                  sx={{
                    bgcolor: notification.read ? 'transparent' : getNotificationColor(notification.type),
                    borderLeft: notification.read ? 'none' : `4px solid ${
                      notification.type === 'success' ? colors.health[500] :
                      notification.type === 'warning' ? colors.warning :
                      notification.type === 'error' ? colors.error :
                      notification.type === 'reminder' ? colors.energy[500] :
                      colors.info
                    }`,
                    '&:hover': {
                      bgcolor: colors.professional[50]
                    }
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    {getNotificationIcon(notification.type)}
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            fontWeight: notification.read ? 400 : 600,
                            color: colors.professional[700]
                          }}
                        >
                          {notification.title}
                        </Typography>
                        {!notification.read && (
                          <Box sx={{ 
                            width: 8, 
                            height: 8, 
                            borderRadius: '50%', 
                            bgcolor: colors.energy[500] 
                          }} />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" sx={{ color: colors.professional[600], mb: 0.5 }}>
                          {notification.message}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Typography variant="caption" sx={{ color: colors.professional[500] }}>
                            {formatTimestamp(notification.timestamp)}
                          </Typography>
                          {notification.actionLabel && (
                            <Chip 
                              label={notification.actionLabel}
                              size="small"
                              sx={{ 
                                bgcolor: colors.energy[100],
                                color: colors.energy[700],
                                fontSize: '0.75rem',
                                height: 20
                              }}
                            />
                          )}
                        </Box>
                      </Box>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    <IconButton 
                      size="small" 
                      onClick={(e) => handleMenuClick(e, notification.id)}
                    >
                      <MoreIcon fontSize="small" />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
                
                {index < recentNotifications.length - 1 && (
                  <Divider sx={{ borderColor: colors.professional[100] }} />
                )}
              </AnimatedBox>
            ))}
          </List>
        )}

        {notifications.length > 10 && (
          <Box sx={{ p: 2, borderTop: `1px solid ${colors.professional[200]}`, textAlign: 'center' }}>
            <Button size="small" sx={{ color: colors.professional[600] }}>
              Ver todas as notificações
            </Button>
          </Box>
        )}
      </Popover>

      {/* Menu de ações da notificação */}
      <Menu
        anchorEl={menuAnchorEl}
        open={isMenuOpen}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 4px 16px rgba(0, 0, 0, 0.12)'
          }
        }}
      >
        {selectedNotification && (
          <>
            {!notifications.find(n => n.id === selectedNotification)?.read && (
              <MenuItem onClick={() => handleMarkAsRead(selectedNotification)}>
                <DoneAllIcon sx={{ mr: 1, fontSize: 18 }} />
                Marcar como lida
              </MenuItem>
            )}
            <MenuItem onClick={() => handleRemove(selectedNotification)}>
              <ClearIcon sx={{ mr: 1, fontSize: 18 }} />
              Remover
            </MenuItem>
          </>
        )}
      </Menu>
    </>
  );
};

export default NotificationCenter;
