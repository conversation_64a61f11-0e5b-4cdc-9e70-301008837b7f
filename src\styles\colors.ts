export const colors = {
  // === PALETA NUBANK - BRANCO E ROXO ELEGANTE ===

  // Cores principais - Roxo elegante e branco limpo
  primary: '#8A2BE2', // Roxo elegante (inspirado no Nubank)
  secondary: '#FFFFFF', // Branco puro
  accent: '#9C27B0', // Roxo secund<PERSON>rio (variação)

  // Cores funcionais
  success: '#4CAF50', // Verde natural (sucesso)
  warning: '#FF9800', // Laranja suave (atenção)
  error: '#F44336', // Vermelho suave (erro)
  info: '#2196F3', // Azul informativo (informação)

  // Paleta principal - Tons de roxo e branco
  dark: '#4A148C', // Roxo muito escuro
  medium: '#7B1FA2', // Roxo médio
  light: '#E1BEE7', // Roxo muito claro
  white: '#FFFFFF', // Branco puro

  // Tons de roxo (variação da paleta principal)
  purple: {
    50: '#F3E5F5', // Roxo muito claro
    100: '#E1BEE7', // Roxo claro
    200: '#CE93D8', // Roxo suave
    300: '#BA68C8', // Roxo médio claro
    400: '#AB47BC', // Roxo médio
    500: '#9C27B0', // Roxo principal (ACCENT)
    600: '#8E24AA', // Roxo escuro
    700: '#7B1FA2', // Roxo mais escuro
    800: '#6A1B9A', // Roxo muito escuro
    900: '#4A148C', // Roxo profundo
  },

  // Tons de branco e cinza (neutros)
  neutral: {
    50: '#FFFFFF', // Branco puro
    100: '#F5F5F5', // Cinza muito claro
    200: '#EEEEEE', // Cinza claro
    300: '#E0E0E0', // Cinza suave
    400: '#BDBDBD', // Cinza médio
    500: '#9E9E9E', // Cinza médio escuro
    600: '#757575', // Cinza escuro
    700: '#616161', // Cinza muito escuro
    800: '#424242', // Cinza profundo
    900: '#212121', // Cinza muito profundo
  },

  // Tons de funcionalidade
  functional: {
    success: '#4CAF50', // Verde sucesso
    warning: '#FF9800', // Laranja aviso
    error: '#F44336', // Vermelho erro
    info: '#2196F3', // Azul informação
  },

  // Opacidades (em hexadecimal)
  opacity: {
    5: '0D',   // 5%
    10: '1A',  // 10%
    15: '26',  // 15%
    20: '33',  // 20%
    25: '40',  // 25%
    30: '4D',  // 30%
    40: '66',  // 40%
    50: '80',  // 50%
    60: '99',  // 60%
    70: 'B3',  // 70%
    80: 'CC',  // 80%
    90: 'E6',  // 90%
    95: 'F2',  // 95%
  },

  // Gradientes modernos - roxo elegante
  gradients: {
    primary: 'linear-gradient(135deg, #8A2BE2 0%, #9C27B0 100%)', // Roxo elegante
    secondary: 'linear-gradient(135deg, #FFFFFF 0%, #F5F5F5 100%)', // Branco suave
    success: 'linear-gradient(135deg, #4CAF50 0%, #388E3C 100%)', // Verde sucesso
    accent: 'linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%)', // Roxo acento
    calm: 'linear-gradient(135deg, #FFFFFF 0%, #EEEEEE 100%)', // Branco tranquilo
  },

  // Sombras suaves e modernas - roxo elegante
  shadows: {
    xs: '0px 1px 2px rgba(138, 43, 226, 0.05)', // Muito sutil
    sm: '0px 2px 4px rgba(138, 43, 226, 0.08)', // Sutil
    md: '0px 4px 8px rgba(138, 43, 226, 0.12)', // Médio
    lg: '0px 8px 16px rgba(138, 43, 226, 0.15)', // Grande
    xl: '0px 12px 24px rgba(138, 43, 226, 0.18)', // Extra grande
    purple: '0px 4px 12px rgba(138, 43, 226, 0.25)', // Sombra roxa
    success: '0px 4px 12px rgba(76, 175, 80, 0.25)', // Sombra de sucesso
  },

  // Textos com melhor contraste
  text: {
    primary: '#212121', // Cinza muito escuro
    secondary: '#616161', // Cinza escuro
    tertiary: '#9E9E9E', // Cinza médio
    disabled: '#BDBDBD', // Cinza claro
    inverse: '#FFFFFF', // Branco para fundos escuros
  },

  // === ALIASES PARA COMPATIBILIDADE ===
  // (mantém compatibilidade com código existente)
  ocean: '#8A2BE2', // = primary (roxo)
  sea: '#9C27B0', // = accent (roxo secundário)
  cloud: '#FFFFFF', // = white (branco)

  // Tons de cinza (compatibilidade)
  gray: {
    50: '#FFFFFF',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },

  // Paleta profissional - tons de azul/cinza para elementos corporativos
  professional: {
    50: '#F8FAFC',   // Azul muito claro
    100: '#F1F5F9',  // Azul claro
    200: '#E2E8F0',  // Azul suave
    300: '#CBD5E1',  // Azul médio claro
    400: '#94A3B8',  // Azul médio
    500: '#64748B',  // Azul principal
    600: '#475569',  // Azul escuro
    700: '#334155',  // Azul mais escuro
    800: '#1E293B',  // Azul muito escuro
    900: '#0F172A',  // Azul profundo
  },

  // Paleta de saúde - tons de verde para elementos de bem-estar
  health: {
    50: '#F0FDF4',   // Verde muito claro
    100: '#DCFCE7',  // Verde claro
    200: '#BBF7D0',  // Verde suave
    300: '#86EFAC',  // Verde médio claro
    400: '#4ADE80',  // Verde médio
    500: '#22C55E',  // Verde principal
    600: '#16A34A',  // Verde escuro
    700: '#15803D',  // Verde mais escuro
    800: '#166534',  // Verde muito escuro
    900: '#14532D',  // Verde profundo
  },

  // Paleta de energia - tons de laranja para elementos motivacionais
  energy: {
    50: '#FFF7ED',   // Laranja muito claro
    100: '#FFEDD5',  // Laranja claro
    200: '#FED7AA',  // Laranja suave
    300: '#FDBA74',  // Laranja médio claro
    400: '#FB923C',  // Laranja médio
    500: '#F97316',  // Laranja principal
    600: '#EA580C',  // Laranja escuro
    700: '#C2410C',  // Laranja mais escuro
    800: '#9A3412',  // Laranja muito escuro
    900: '#7C2D12',  // Laranja profundo
  }
};

// ============================================================================
// DARK THEME - PALETA PARA MODO ESCURO
// ============================================================================

export const darkColors = {
  // Cores principais adaptadas para dark mode - roxo elegante
  primary: '#9C27B0', // Roxo mais claro para melhor contraste
  secondary: '#4A148C', // Roxo escuro mais suave
  accent: '#BA68C8', // Roxo mais vibrante

  // Cores funcionais para dark mode
  success: '#66BB6A',
  warning: '#FFA726',
  error: '#EF5350',
  info: '#42A5F5',

  // Backgrounds escuros - roxo elegante
  background: {
    primary: '#1A0A2E', // Roxo muito escuro
    secondary: '#2D1B4D', // Roxo escuro
    tertiary: '#4A2D7A', // Roxo médio escuro
    paper: '#2D1B4D', // Fundo de cards
    elevated: '#4A2D7A', // Fundo elevado
  },

  // Superfícies escuras
  surface: {
    primary: '#2D1B4D',
    secondary: '#4A2D7A',
    tertiary: '#6A4A9C',
    hover: '#6A4A9C',
    selected: '#8A6AAA',
  },

  // Textos para dark mode
  text: {
    primary: '#FFFFFF', // Branco puro
    secondary: '#E1BEE7', // Roxo claro
    tertiary: '#CE93D8', // Roxo médio
    disabled: '#BA68C8', // Roxo suave
    inverse: '#1A0A2E', // Roxo muito escuro para fundos claros
  },

  // Bordas para dark mode
  border: {
    primary: '#4A2D7A',
    secondary: '#6A4A9C',
    tertiary: '#8A6AAA',
    focus: '#9C27B0',
  },

  // Tons de roxo adaptados para dark mode
  purple: {
    50: '#1A0A2E',
    100: '#2D1B4D',
    200: '#4A2D7A',
    300: '#6A4A9C',
    400: '#8A6AAA',
    500: '#9C27B0', // Roxo principal adaptado
    600: '#AB47BC',
    700: '#BA68C8',
    800: '#CE93D8',
    900: '#E1BEE7',
  },

  // Tons neutros adaptados para dark mode
  neutral: {
    50: '#1A0A2E',
    100: '#2D1B4D',
    200: '#4A2D7A',
    300: '#6A4A9C',
    400: '#8A6AAA',
    500: '#A88AC9',
    600: '#C6AAD8',
    700: '#E1BEE7',
    800: '#F3E5F5',
    900: '#FFFFFF',
  },

  // Tons funcionais adaptados para dark mode
  functional: {
    success: '#66BB6A',
    warning: '#FFA726',
    error: '#EF5350',
    info: '#42A5F5',
  },

  // Sombras para dark mode - roxo elegante
  shadows: {
    xs: '0px 1px 2px rgba(0, 0, 0, 0.3)',
    sm: '0px 2px 4px rgba(0, 0, 0, 0.4)',
    md: '0px 4px 8px rgba(0, 0, 0, 0.5)',
    lg: '0px 8px 16px rgba(0, 0, 0, 0.6)',
    xl: '0px 12px 24px rgba(0, 0, 0, 0.7)',
    purple: '0px 4px 12px rgba(156, 39, 176, 0.4)',
    success: '0px 4px 12px rgba(102, 187, 106, 0.4)',
  },

  // Gradientes para dark mode - roxo elegante
  gradients: {
    primary: 'linear-gradient(135deg, #9C27B0 0%, #BA68C8 100%)',
    secondary: 'linear-gradient(135deg, #2D1B4D 0%, #4A2D7A 100%)',
    success: 'linear-gradient(135deg, #66BB6A 0%, #43A047 100%)',
    accent: 'linear-gradient(135deg, #BA68C8 0%, #9C27B0 100%)',
    calm: 'linear-gradient(135deg, #4A2D7A 0%, #6A4A9C 100%)',
  },

  // Paleta profissional para dark mode - tons de azul/cinza escuros
  professional: {
    50: '#0F172A',   // Azul muito escuro
    100: '#1E293B',  // Azul escuro
    200: '#334155',  // Azul médio escuro
    300: '#475569',  // Azul médio
    400: '#64748B',  // Azul claro
    500: '#94A3B8',  // Azul principal
    600: '#CBD5E1',  // Azul mais claro
    700: '#E2E8F0',  // Azul muito claro
    800: '#F1F5F9',  // Azul quase branco
    900: '#F8FAFC',  // Azul branco
  },

  // Paleta de saúde para dark mode - tons de verde escuros
  health: {
    50: '#14532D',   // Verde muito escuro
    100: '#166534',  // Verde escuro
    200: '#15803D',  // Verde médio escuro
    300: '#16A34A',  // Verde médio
    400: '#22C55E',  // Verde claro
    500: '#4ADE80',  // Verde principal
    600: '#86EFAC',  // Verde mais claro
    700: '#BBF7D0',  // Verde muito claro
    800: '#DCFCE7',  // Verde quase branco
    900: '#F0FDF4',  // Verde branco
  },

  // Paleta de energia para dark mode - tons de laranja escuros
  energy: {
    50: '#7C2D12',   // Laranja muito escuro
    100: '#9A3412',  // Laranja escuro
    200: '#C2410C',  // Laranja médio escuro
    300: '#EA580C',  // Laranja médio
    400: '#F97316',  // Laranja claro
    500: '#FB923C',  // Laranja principal
    600: '#FDBA74',  // Laranja mais claro
    700: '#FED7AA',  // Laranja muito claro
    800: '#FFEDD5',  // Laranja quase branco
    900: '#FFF7ED',  // Laranja branco
  }
};

// ============================================================================
// THEME UTILITIES - UTILITÁRIOS PARA GERENCIAMENTO DE TEMA
// ============================================================================

export const getThemeColors = (isDark: boolean) => {
  return isDark ? darkColors : colors;
};

export const createThemeAwareColor = (lightColor: string, darkColor: string) => {
  return `var(--theme-aware-color, ${lightColor})`;
};

// CSS Variables para tema dinâmico
export const cssVariables = {
  light: {
    '--bg-primary': colors.white,
    '--bg-secondary': colors.professional[50],
    '--bg-tertiary': colors.professional[100],
    '--text-primary': colors.text.primary,
    '--text-secondary': colors.text.secondary,
    '--border-primary': colors.professional[200],
    '--shadow-primary': colors.shadows.md,
  },
  dark: {
    '--bg-primary': darkColors.background.primary,
    '--bg-secondary': darkColors.background.secondary,
    '--bg-tertiary': darkColors.background.tertiary,
    '--text-primary': darkColors.text.primary,
    '--text-secondary': darkColors.text.secondary,
    '--border-primary': darkColors.border.primary,
    '--shadow-primary': darkColors.shadows.md,
  }
};