import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  TextField,
  InputAdornment,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Typography,
  Chip,
  Divider,
  IconButton,
  Fade,
  Popper,
  ClickAwayListener,
  Avatar
} from '@mui/material';
import {
  Search as SearchIcon,
  Person as PersonIcon,
  Assessment as AssessmentIcon,
  FitnessCenter as FitnessCenterIcon,
  History as HistoryIcon,
  Clear as ClearIcon,
  TrendingUp as TrendingUpIcon,
  AccessTime as TimeIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { Cliente } from '../../models/Cliente';
import { AvaliacaoFisica } from '../../models/AvaliacaoFisica';
import { Treino } from '../../models/Treino';
import { useAppContext } from '../../contexts/AppContext';
import { colors } from '../../styles/colors';
import { AnimatedBox } from './AnimatedComponents';

// ============================================================================
// GLOBAL SEARCH - BUSCA GLOBAL INTELIGENTE
// ============================================================================

export interface SearchResult {
  id: string;
  type: 'cliente' | 'avaliacao' | 'treino' | 'exercicio';
  title: string;
  subtitle: string;
  description?: string;
  data: any;
  relevance: number;
  lastAccessed?: Date;
}

interface GlobalSearchProps {
  placeholder?: string;
  maxResults?: number;
  showHistory?: boolean;
  onResultSelect?: (result: SearchResult) => void;
}

export const GlobalSearch: React.FC<GlobalSearchProps> = ({
  placeholder = 'Buscar em tudo...',
  maxResults = 8,
  showHistory = true,
  onResultSelect
}) => {
  const navigate = useNavigate();
  const { clientes } = useAppContext();
  const [searchTerm, setSearchTerm] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [searchHistory, setSearchHistory] = useState<SearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Carregar histórico do localStorage
  useEffect(() => {
    const savedHistory = localStorage.getItem('globalSearchHistory');
    if (savedHistory) {
      try {
        setSearchHistory(JSON.parse(savedHistory));
      } catch (error) {
        console.error('Erro ao carregar histórico de busca:', error);
      }
    }
  }, []);

  // Salvar histórico no localStorage
  const saveToHistory = (result: SearchResult) => {
    const newHistory = [
      result,
      ...searchHistory.filter(item => item.id !== result.id)
    ].slice(0, 10); // Manter apenas os 10 mais recentes

    setSearchHistory(newHistory);
    localStorage.setItem('globalSearchHistory', JSON.stringify(newHistory));
  };

  // Função de busca principal
  const performSearch = async (term: string): Promise<SearchResult[]> => {
    if (!term.trim()) return [];

    setLoading(true);
    const results: SearchResult[] = [];

    try {
      // Buscar clientes
      const clienteResults = searchClientes(term);
      results.push(...clienteResults);

      // Buscar avaliações (simulado - implementar com dados reais)
      const avaliacaoResults = searchAvaliacoes(term);
      results.push(...avaliacaoResults);

      // Buscar treinos (simulado - implementar com dados reais)
      const treinoResults = searchTreinos(term);
      results.push(...treinoResults);

      // Ordenar por relevância
      results.sort((a, b) => b.relevance - a.relevance);

      return results.slice(0, maxResults);
    } finally {
      setLoading(false);
    }
  };

  // Buscar clientes
  const searchClientes = (term: string): SearchResult[] => {
    const termLower = term.toLowerCase();
    
    return clientes
      .filter(cliente => 
        cliente.nome.toLowerCase().includes(termLower) ||
        cliente.email?.toLowerCase().includes(termLower) ||
        cliente.telefone?.includes(term)
      )
      .map(cliente => ({
        id: `cliente-${cliente.id}`,
        type: 'cliente' as const,
        title: cliente.nome,
        subtitle: cliente.email || cliente.telefone || '',
        description: `${cliente.sexo === 'M' ? 'Masculino' : 'Feminino'} • Cadastrado em ${new Date(cliente.data_cadastro).toLocaleDateString('pt-BR')}`,
        data: cliente,
        relevance: calculateRelevance(term, [cliente.nome, cliente.email || '', cliente.telefone || ''])
      }));
  };

  // Buscar avaliações (implementar com dados reais)
  const searchAvaliacoes = (term: string): SearchResult[] => {
    // Implementar busca em avaliações físicas
    return [];
  };

  // Buscar treinos (implementar com dados reais)
  const searchTreinos = (term: string): SearchResult[] => {
    // Implementar busca em treinos
    return [];
  };

  // Calcular relevância da busca
  const calculateRelevance = (term: string, fields: string[]): number => {
    const termLower = term.toLowerCase();
    let relevance = 0;

    fields.forEach((field, index) => {
      const fieldLower = field.toLowerCase();
      if (fieldLower === termLower) {
        relevance += 100 - (index * 10); // Correspondência exata
      } else if (fieldLower.startsWith(termLower)) {
        relevance += 80 - (index * 10); // Começa com o termo
      } else if (fieldLower.includes(termLower)) {
        relevance += 60 - (index * 10); // Contém o termo
      }
    });

    return relevance;
  };

  // Debounce da busca
  useEffect(() => {
    const timeoutId = setTimeout(async () => {
      if (searchTerm.trim()) {
        const searchResults = await performSearch(searchTerm);
        setResults(searchResults);
      } else {
        setResults([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Manipular seleção de resultado
  const handleResultSelect = (result: SearchResult) => {
    saveToHistory(result);
    setIsOpen(false);
    setSearchTerm('');

    // Navegar para a página apropriada
    switch (result.type) {
      case 'cliente':
        navigate('/clientes');
        // Implementar seleção do cliente específico
        break;
      case 'avaliacao':
        navigate('/avaliacoes');
        break;
      case 'treino':
        navigate('/treinos');
        break;
    }

    onResultSelect?.(result);
  };

  // Limpar histórico
  const clearHistory = () => {
    setSearchHistory([]);
    localStorage.removeItem('globalSearchHistory');
  };

  // Obter ícone por tipo
  const getResultIcon = (type: string) => {
    switch (type) {
      case 'cliente': return <PersonIcon sx={{ color: colors.energy[500] }} />;
      case 'avaliacao': return <AssessmentIcon sx={{ color: colors.health[500] }} />;
      case 'treino': return <FitnessCenterIcon sx={{ color: colors.professional[500] }} />;
      default: return <SearchIcon sx={{ color: colors.professional[400] }} />;
    }
  };

  // Obter cor do chip por tipo
  const getChipColor = (type: string) => {
    switch (type) {
      case 'cliente': return { bg: colors.energy[50], color: colors.energy[700] };
      case 'avaliacao': return { bg: colors.health[50], color: colors.health[700] };
      case 'treino': return { bg: colors.professional[50], color: colors.professional[700] };
      default: return { bg: colors.professional[50], color: colors.professional[600] };
    }
  };

  const displayResults = searchTerm.trim() ? results : (showHistory ? searchHistory : []);
  const showHistoryHeader = !searchTerm.trim() && showHistory && searchHistory.length > 0;

  return (
    <ClickAwayListener onClickAway={() => setIsOpen(false)}>
      <Box ref={searchRef} sx={{ position: 'relative', width: '100%', maxWidth: 500 }}>
        <TextField
          ref={inputRef}
          fullWidth
          size="small"
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onFocus={() => setIsOpen(true)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: colors.professional[400] }} />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <IconButton
                  size="small"
                  onClick={() => {
                    setSearchTerm('');
                    setResults([]);
                  }}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ),
            sx: {
              borderRadius: 3,
              bgcolor: colors.white,
              '& fieldset': {
                borderColor: colors.professional[300],
              },
              '&:hover fieldset': {
                borderColor: colors.energy[400],
              },
              '&.Mui-focused fieldset': {
                borderColor: colors.energy[500],
              }
            }
          }}
        />

        <Popper
          open={isOpen && (displayResults.length > 0 || showHistoryHeader)}
          anchorEl={searchRef.current}
          placement="bottom-start"
          style={{ width: searchRef.current?.offsetWidth, zIndex: 1300 }}
        >
          <Fade in={isOpen}>
            <Paper
              elevation={8}
              sx={{
                mt: 1,
                borderRadius: 3,
                border: `1px solid ${colors.professional[200]}`,
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
                maxHeight: 400,
                overflow: 'auto'
              }}
            >
              {showHistoryHeader && (
                <Box sx={{ p: 2, pb: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Typography variant="subtitle2" sx={{ color: colors.professional[600], display: 'flex', alignItems: 'center', gap: 1 }}>
                      <HistoryIcon fontSize="small" />
                      Buscas Recentes
                    </Typography>
                    <IconButton size="small" onClick={clearHistory}>
                      <ClearIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
              )}

              <List dense sx={{ py: 0 }}>
                {displayResults.map((result, index) => {
                  const chipColor = getChipColor(result.type);
                  
                  return (
                    <AnimatedBox key={result.id} animation="fadeInUp" delay={index * 0.05}>
                      <ListItem
                        button
                        onClick={() => handleResultSelect(result)}
                        sx={{
                          py: 1.5,
                          '&:hover': {
                            bgcolor: colors.energy[50]
                          }
                        }}
                      >
                        <ListItemIcon sx={{ minWidth: 40 }}>
                          {result.type === 'cliente' ? (
                            <Avatar sx={{ width: 32, height: 32, bgcolor: colors.energy[100], color: colors.energy[600], fontSize: '0.875rem' }}>
                              {result.title.split(' ').map(n => n[0]).join('').slice(0, 2)}
                            </Avatar>
                          ) : (
                            getResultIcon(result.type)
                          )}
                        </ListItemIcon>
                        
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body2" sx={{ fontWeight: 500, color: colors.professional[700] }}>
                                {result.title}
                              </Typography>
                              <Chip
                                label={result.type}
                                size="small"
                                sx={{
                                  bgcolor: chipColor.bg,
                                  color: chipColor.color,
                                  fontSize: '0.75rem',
                                  height: 20
                                }}
                              />
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" sx={{ color: colors.professional[600] }}>
                                {result.subtitle}
                              </Typography>
                              {result.description && (
                                <Typography variant="caption" sx={{ color: colors.professional[500] }}>
                                  {result.description}
                                </Typography>
                              )}
                            </Box>
                          }
                        />

                        {!searchTerm.trim() && (
                          <ListItemSecondaryAction>
                            <TimeIcon sx={{ color: colors.professional[400], fontSize: 16 }} />
                          </ListItemSecondaryAction>
                        )}
                      </ListItem>
                      
                      {index < displayResults.length - 1 && (
                        <Divider sx={{ borderColor: colors.professional[100] }} />
                      )}
                    </AnimatedBox>
                  );
                })}
              </List>

              {searchTerm.trim() && results.length === 0 && !loading && (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <SearchIcon sx={{ color: colors.professional[300], fontSize: 48, mb: 1 }} />
                  <Typography variant="body2" sx={{ color: colors.professional[500] }}>
                    Nenhum resultado encontrado para "{searchTerm}"
                  </Typography>
                </Box>
              )}
            </Paper>
          </Fade>
        </Popper>
      </Box>
    </ClickAwayListener>
  );
};

export default GlobalSearch;
