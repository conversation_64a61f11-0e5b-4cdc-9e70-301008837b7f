const webpack = require('webpack');
const path = require('path');

module.exports = function override(config) {
  const fallback = {
    "process": require.resolve("process/browser"),
    "zlib": require.resolve("browserify-zlib"),
    "stream": require.resolve("stream-browserify"),
    "assert": require.resolve("assert"),
    "http": require.resolve("stream-http"),
    "https": require.resolve("https-browserify"),
    "os": require.resolve("os-browserify"),
    "url": require.resolve("url"),
    "path": require.resolve("path-browserify"),
    "fs": false,
    "crypto": require.resolve("crypto-browserify"),
    "buffer": require.resolve("buffer")
  };

  config.resolve.fallback = {
    ...config.resolve.fallback,
    ...fallback
  };

  config.plugins = (config.plugins || []).concat([
    new webpack.ProvidePlugin({
      process: 'process/browser',
      <PERSON>uffer: ['buffer', 'Buffer']
    })
  ]);

  // Configure module resolution to prefer local node_modules
  config.resolve.modules = [
    path.resolve(__dirname, 'src'),
    path.resolve(__dirname, 'node_modules'),
    'node_modules'
  ];

  // Add symlinks resolution
  config.resolve.symlinks = false;

  return config;
};