import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Container,
  Grid,
  Paper
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email,
  Lock,
  Person,
  Phone,
  Business
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { colors } from '../styles/colors';
import { AnimatedBox, FadeInWhenVisible } from '../components/common/AnimatedComponents';

const NewRegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const { register, isAuthenticated, isLoading, error, clearError } = useAuth();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    clinic_name: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  // Redirecionar se já estiver autenticado
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // Limpar erro quando componente desmonta
  useEffect(() => {
    return () => clearError();
  }, [clearError]);

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
    // Limpar erro do campo quando usuário começar a digitar
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      errors.name = 'Nome é obrigatório';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email é obrigatório';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email inválido';
    }

    if (!formData.password) {
      errors.password = 'Senha é obrigatória';
    } else if (formData.password.length < 6) {
      errors.password = 'Senha deve ter pelo menos 6 caracteres';
    }

    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Senhas não coincidem';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    clearError();

    if (!validateForm()) {
      return;
    }

    const userData = {
      name: formData.name,
      email: formData.email,
      password: formData.password,
      phone: formData.phone || undefined,
      clinic_name: formData.clinic_name || undefined,
      role: 'trainer' as const,
      is_active: true
    };

    const success = await register(userData);
    if (success) {
      navigate('/dashboard');
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${colors.purple[900]} 0%, ${colors.purple[700]} 50%, ${colors.purple[500]} 100%)`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2,
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Background Animation Elements */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.1,
          background: `
            radial-gradient(circle at 20% 80%, ${colors.energy[400]} 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, ${colors.health[400]} 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, ${colors.professional[400]} 0%, transparent 50%)
          `
        }}
      />

      <Container maxWidth="md">
        <AnimatedBox animation="fadeInUp" duration={1}>
          <Paper
            elevation={0}
            sx={{
              background: `linear-gradient(135deg, ${colors.purple[800]}90, ${colors.purple[700]}90)`,
              backdropFilter: 'blur(20px)',
              border: `1px solid ${colors.energy[400]}30`,
              borderRadius: 4,
              p: 4,
              maxWidth: 600,
              mx: 'auto'
            }}
          >
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography 
                variant="h3" 
                fontWeight="bold"
                sx={{ 
                  color: colors.white,
                  mb: 1,
                  textShadow: `0 0 20px ${colors.energy[400]}80`
                }}
              >
                KHATFIT
              </Typography>
              <Typography 
                variant="h5" 
                sx={{ 
                  color: colors.energy[300],
                  mb: 1
                }}
              >
                Criar Conta
              </Typography>
              <Typography 
                variant="body1" 
                sx={{ 
                  color: colors.white,
                  opacity: 0.8
                }}
              >
                Junte-se ao nosso time
              </Typography>
            </Box>

            {error && (
              <Alert 
                severity="error" 
                sx={{ 
                  mb: 3,
                  bgcolor: `${colors.error}20`,
                  color: colors.white,
                  border: `1px solid ${colors.error}40`
                }}
              >
                {error}
              </Alert>
            )}

            <Box component="form" onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                {/* Nome */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    placeholder="Nome Completo"
                    value={formData.name}
                    onChange={handleInputChange('name')}
                    error={!!formErrors.name}
                    helperText={formErrors.name}
                    required
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: `${colors.purple[900]}60`,
                        borderRadius: 3,
                        '& fieldset': {
                          borderColor: `${colors.energy[400]}40`,
                        },
                        '&:hover fieldset': {
                          borderColor: `${colors.energy[400]}60`,
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: colors.energy[400],
                        },
                        '& input': {
                          color: colors.white,
                          '&::placeholder': {
                            color: `${colors.white}60`,
                            opacity: 1
                          }
                        }
                      },
                      '& .MuiFormHelperText-root': {
                        color: colors.error
                      }
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Person sx={{ color: colors.energy[400] }} />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>

                {/* Email */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    placeholder="Email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    error={!!formErrors.email}
                    helperText={formErrors.email}
                    required
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: `${colors.purple[900]}60`,
                        borderRadius: 3,
                        '& fieldset': {
                          borderColor: `${colors.energy[400]}40`,
                        },
                        '&:hover fieldset': {
                          borderColor: `${colors.energy[400]}60`,
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: colors.energy[400],
                        },
                        '& input': {
                          color: colors.white,
                          '&::placeholder': {
                            color: `${colors.white}60`,
                            opacity: 1
                          }
                        }
                      },
                      '& .MuiFormHelperText-root': {
                        color: colors.error
                      }
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Email sx={{ color: colors.energy[400] }} />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>

                {/* Telefone */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    placeholder="Telefone (opcional)"
                    value={formData.phone}
                    onChange={handleInputChange('phone')}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: `${colors.purple[900]}60`,
                        borderRadius: 3,
                        '& fieldset': {
                          borderColor: `${colors.energy[400]}40`,
                        },
                        '&:hover fieldset': {
                          borderColor: `${colors.energy[400]}60`,
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: colors.energy[400],
                        },
                        '& input': {
                          color: colors.white,
                          '&::placeholder': {
                            color: `${colors.white}60`,
                            opacity: 1
                          }
                        }
                      }
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Phone sx={{ color: colors.energy[400] }} />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>

                {/* Academia */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    placeholder="Academia/Estúdio (opcional)"
                    value={formData.clinic_name}
                    onChange={handleInputChange('clinic_name')}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: `${colors.purple[900]}60`,
                        borderRadius: 3,
                        '& fieldset': {
                          borderColor: `${colors.energy[400]}40`,
                        },
                        '&:hover fieldset': {
                          borderColor: `${colors.energy[400]}60`,
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: colors.energy[400],
                        },
                        '& input': {
                          color: colors.white,
                          '&::placeholder': {
                            color: `${colors.white}60`,
                            opacity: 1
                          }
                        }
                      }
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Business sx={{ color: colors.energy[400] }} />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                {/* Senha */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    placeholder="Senha"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleInputChange('password')}
                    error={!!formErrors.password}
                    helperText={formErrors.password}
                    required
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: `${colors.purple[900]}60`,
                        borderRadius: 3,
                        '& fieldset': {
                          borderColor: `${colors.energy[400]}40`,
                        },
                        '&:hover fieldset': {
                          borderColor: `${colors.energy[400]}60`,
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: colors.energy[400],
                        },
                        '& input': {
                          color: colors.white,
                          '&::placeholder': {
                            color: `${colors.white}60`,
                            opacity: 1
                          }
                        }
                      },
                      '& .MuiFormHelperText-root': {
                        color: colors.error
                      }
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock sx={{ color: colors.energy[400] }} />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                            sx={{ color: colors.energy[400] }}
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>

                {/* Confirmar Senha */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    placeholder="Confirmar Senha"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={handleInputChange('confirmPassword')}
                    error={!!formErrors.confirmPassword}
                    helperText={formErrors.confirmPassword}
                    required
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: `${colors.purple[900]}60`,
                        borderRadius: 3,
                        '& fieldset': {
                          borderColor: `${colors.energy[400]}40`,
                        },
                        '&:hover fieldset': {
                          borderColor: `${colors.energy[400]}60`,
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: colors.energy[400],
                        },
                        '& input': {
                          color: colors.white,
                          '&::placeholder': {
                            color: `${colors.white}60`,
                            opacity: 1
                          }
                        }
                      },
                      '& .MuiFormHelperText-root': {
                        color: colors.error
                      }
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock sx={{ color: colors.energy[400] }} />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            edge="end"
                            sx={{ color: colors.energy[400] }}
                          >
                            {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
              </Grid>

              {/* Botões */}
              <Box sx={{ mt: 4 }}>
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={isLoading}
                  sx={{
                    py: 2,
                    mb: 3,
                    background: `linear-gradient(45deg, ${colors.energy[500]}, ${colors.health[500]})`,
                    borderRadius: 3,
                    fontWeight: 700,
                    fontSize: '1.1rem',
                    textTransform: 'uppercase',
                    letterSpacing: 1,
                    boxShadow: `0 8px 32px ${colors.energy[500]}40`,
                    '&:hover': {
                      background: `linear-gradient(45deg, ${colors.energy[600]}, ${colors.health[600]})`,
                      boxShadow: `0 12px 40px ${colors.energy[500]}60`,
                      transform: 'translateY(-2px)'
                    },
                    '&:disabled': {
                      background: `${colors.gray[600]}`,
                      color: colors.gray[400]
                    },
                    transition: 'all 0.3s ease'
                  }}
                >
                  {isLoading ? 'CRIANDO CONTA...' : 'CRIAR CONTA'}
                </Button>

                {/* Login Link */}
                <Box sx={{ textAlign: 'center' }}>
                  <Typography
                    variant="body2"
                    sx={{
                      color: colors.white,
                      opacity: 0.7,
                      mb: 2
                    }}
                  >
                    Já tem uma conta?
                  </Typography>

                  <Button
                    component={Link}
                    to="/login"
                    variant="outlined"
                    fullWidth
                    sx={{
                      py: 1.5,
                      borderColor: `${colors.energy[400]}60`,
                      color: colors.energy[300],
                      borderRadius: 3,
                      fontWeight: 600,
                      '&:hover': {
                        borderColor: colors.energy[400],
                        color: colors.energy[400],
                        backgroundColor: `${colors.energy[400]}10`
                      }
                    }}
                  >
                    Fazer Login
                  </Button>
                </Box>
              </Box>
            </Box>
          </Paper>
        </AnimatedBox>
      </Container>
    </Box>
  );
};

export default NewRegisterPage;
