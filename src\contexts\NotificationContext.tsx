import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useSettings } from './SettingsContext';

// ============================================================================
// NOTIFICATION CONTEXT - SISTEMA DE NOTIFICAÇÕES PUSH
// ============================================================================

export interface AppNotification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'reminder';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  persistent?: boolean;
  actionLabel?: string;
  actionUrl?: string;
  clienteId?: number;
  avaliacaoId?: number;
  treinoId?: number;
  expiresAt?: Date;
}

export interface NotificationRule {
  id: string;
  type: 'assessment_reminder' | 'workout_reminder' | 'birthday' | 'inactive_client';
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
  conditions: Record<string, any>;
  lastTriggered?: Date;
}

interface NotificationContextType {
  notifications: AppNotification[];
  unreadCount: number;
  rules: NotificationRule[];
  addNotification: (notification: Omit<AppNotification, 'id' | 'timestamp' | 'read'>) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  removeNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
  updateRule: (ruleId: string, updates: Partial<NotificationRule>) => void;
  checkAndTriggerNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { settings } = useSettings();
  const [notifications, setNotifications] = useState<AppNotification[]>([]);
  const [rules, setRules] = useState<NotificationRule[]>([
    {
      id: 'assessment_reminder',
      type: 'assessment_reminder',
      enabled: true,
      frequency: 'monthly',
      conditions: { daysBetweenAssessments: 30 }
    },
    {
      id: 'workout_reminder',
      type: 'workout_reminder',
      enabled: true,
      frequency: 'weekly',
      conditions: { daysWithoutWorkout: 7 }
    },
    {
      id: 'birthday',
      type: 'birthday',
      enabled: true,
      frequency: 'daily',
      conditions: { daysBeforeBirthday: 1 }
    },
    {
      id: 'inactive_client',
      type: 'inactive_client',
      enabled: true,
      frequency: 'weekly',
      conditions: { daysInactive: 14 }
    }
  ]);

  // Carregar notificações do localStorage
  useEffect(() => {
    const savedNotifications = localStorage.getItem('appNotifications');
    if (savedNotifications) {
      try {
        const parsed = JSON.parse(savedNotifications);
        const validNotifications = parsed
          .map((n: any) => ({
            ...n,
            timestamp: new Date(n.timestamp),
            expiresAt: n.expiresAt ? new Date(n.expiresAt) : undefined
          }))
          .filter((n: AppNotification) => !n.expiresAt || n.expiresAt > new Date());
        
        setNotifications(validNotifications);
      } catch (error) {
        console.error('Erro ao carregar notificações:', error);
      }
    }

    const savedRules = localStorage.getItem('notificationRules');
    if (savedRules) {
      try {
        const parsed = JSON.parse(savedRules);
        setRules(parsed);
      } catch (error) {
        console.error('Erro ao carregar regras de notificação:', error);
      }
    }
  }, []);

  // Salvar notificações no localStorage
  useEffect(() => {
    localStorage.setItem('appNotifications', JSON.stringify(notifications));
  }, [notifications]);

  // Salvar regras no localStorage
  useEffect(() => {
    localStorage.setItem('notificationRules', JSON.stringify(rules));
  }, [rules]);

  // Solicitar permissão para notificações do navegador
  useEffect(() => {
    if (settings.notifications && 'Notification' in window) {
      if (Notification.permission === 'default') {
        Notification.requestPermission();
      }
    }
  }, [settings.notifications]);

  // Verificar notificações periodicamente
  useEffect(() => {
    if (!settings.notifications) return;

    const interval = setInterval(() => {
      checkAndTriggerNotifications();
    }, 60000); // Verificar a cada minuto

    return () => clearInterval(interval);
  }, [settings.notifications]);

  const addNotification = (notification: Omit<AppNotification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: AppNotification = {
      ...notification,
      id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      read: false
    };

    setNotifications(prev => [newNotification, ...prev]);

    // Mostrar notificação do navegador se permitido
    if (settings.notifications && 'Notification' in window && Notification.permission === 'granted') {
      try {
        const browserNotification = new Notification(notification.title, {
          body: notification.message,
          icon: '/KVM-personal-trainer.png',
          badge: '/KVM-personal-trainer.png',
          tag: newNotification.id,
          requireInteraction: notification.persistent
        });

        browserNotification.onclick = () => {
          window.focus();
          if (notification.actionUrl) {
            window.location.href = notification.actionUrl;
          }
          browserNotification.close();
        };

        // Auto-fechar após 5 segundos se não for persistente
        if (!notification.persistent) {
          setTimeout(() => {
            browserNotification.close();
          }, 5000);
        }
      } catch (error) {
        console.error('Erro ao mostrar notificação do navegador:', error);
      }
    }
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  const removeNotification = (notificationId: string) => {
    setNotifications(prev =>
      prev.filter(notification => notification.id !== notificationId)
    );
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const updateRule = (ruleId: string, updates: Partial<NotificationRule>) => {
    setRules(prev =>
      prev.map(rule =>
        rule.id === ruleId ? { ...rule, ...updates } : rule
      )
    );
  };

  const checkAndTriggerNotifications = () => {
    if (!settings.notifications) return;

    const now = new Date();
    
    rules.forEach(rule => {
      if (!rule.enabled) return;

      // Verificar se já foi disparada recentemente
      if (rule.lastTriggered) {
        const timeSinceLastTrigger = now.getTime() - rule.lastTriggered.getTime();
        const frequencyMs = {
          daily: 24 * 60 * 60 * 1000,
          weekly: 7 * 24 * 60 * 60 * 1000,
          monthly: 30 * 24 * 60 * 60 * 1000
        }[rule.frequency];

        if (timeSinceLastTrigger < frequencyMs) return;
      }

      // Verificar condições específicas da regra
      switch (rule.type) {
        case 'assessment_reminder':
          checkAssessmentReminders(rule);
          break;
        case 'workout_reminder':
          checkWorkoutReminders(rule);
          break;
        case 'birthday':
          checkBirthdayReminders(rule);
          break;
        case 'inactive_client':
          checkInactiveClientReminders(rule);
          break;
      }
    });
  };

  const checkAssessmentReminders = (rule: NotificationRule) => {
    // Implementar lógica para verificar clientes que precisam de nova avaliação
    // Esta é uma implementação simplificada
    const shouldTrigger = Math.random() < 0.1; // 10% de chance para demonstração
    
    if (shouldTrigger) {
      addNotification({
        type: 'reminder',
        title: 'Lembrete de Avaliação',
        message: 'Alguns clientes estão com avaliações físicas em atraso.',
        actionLabel: 'Ver Avaliações',
        actionUrl: '/avaliacoes',
        persistent: true
      });

      updateRule(rule.id, { lastTriggered: new Date() });
    }
  };

  const checkWorkoutReminders = (rule: NotificationRule) => {
    // Implementar lógica para verificar clientes sem treino recente
    const shouldTrigger = Math.random() < 0.05; // 5% de chance para demonstração
    
    if (shouldTrigger) {
      addNotification({
        type: 'reminder',
        title: 'Lembrete de Treino',
        message: 'Alguns clientes não têm treinos atualizados.',
        actionLabel: 'Ver Treinos',
        actionUrl: '/treinos'
      });

      updateRule(rule.id, { lastTriggered: new Date() });
    }
  };

  const checkBirthdayReminders = (rule: NotificationRule) => {
    // Implementar lógica para verificar aniversários
    const shouldTrigger = Math.random() < 0.02; // 2% de chance para demonstração
    
    if (shouldTrigger) {
      addNotification({
        type: 'info',
        title: 'Aniversário do Cliente',
        message: 'Um cliente faz aniversário hoje! 🎉',
        actionLabel: 'Ver Cliente',
        actionUrl: '/clientes'
      });

      updateRule(rule.id, { lastTriggered: new Date() });
    }
  };

  const checkInactiveClientReminders = (rule: NotificationRule) => {
    // Implementar lógica para verificar clientes inativos
    const shouldTrigger = Math.random() < 0.03; // 3% de chance para demonstração
    
    if (shouldTrigger) {
      addNotification({
        type: 'warning',
        title: 'Clientes Inativos',
        message: 'Alguns clientes não têm atividade recente.',
        actionLabel: 'Ver Clientes',
        actionUrl: '/clientes'
      });

      updateRule(rule.id, { lastTriggered: new Date() });
    }
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    rules,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,
    updateRule,
    checkAndTriggerNotifications
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications deve ser usado dentro de um NotificationProvider');
  }
  return context;
};

export default NotificationContext;
