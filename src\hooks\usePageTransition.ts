import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

// ============================================================================
// HOOK PARA TRANSIÇÕES DE PÁGINA
// ============================================================================

interface PageTransitionState {
  isTransitioning: boolean;
  direction: 'in' | 'out';
  previousPath: string | null;
  currentPath: string;
}

export const usePageTransition = (duration: number = 300) => {
  const location = useLocation();
  const [transitionState, setTransitionState] = useState<PageTransitionState>({
    isTransitioning: false,
    direction: 'in',
    previousPath: null,
    currentPath: location.pathname
  });

  useEffect(() => {
    // Inicia transição de saída
    setTransitionState(prev => ({
      ...prev,
      isTransitioning: true,
      direction: 'out',
      previousPath: prev.currentPath
    }));

    // Após um pequeno delay, muda para transição de entrada
    const timer = setTimeout(() => {
      setTransitionState(prev => ({
        ...prev,
        direction: 'in',
        currentPath: location.pathname
      }));

      // Finaliza a transição
      const endTimer = setTimeout(() => {
        setTransitionState(prev => ({
          ...prev,
          isTransitioning: false
        }));
      }, duration);

      return () => clearTimeout(endTimer);
    }, duration / 2);

    return () => clearTimeout(timer);
  }, [location.pathname, duration]);

  return transitionState;
};

// ============================================================================
// HOOK PARA ANIMAÇÕES DE SCROLL
// ============================================================================

export const useScrollAnimation = (threshold: number = 0.1) => {
  const [visibleElements, setVisibleElements] = useState<Set<string>>(new Set());

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const elementId = entry.target.getAttribute('data-animate-id');
          if (elementId) {
            setVisibleElements(prev => {
              const newSet = new Set(prev);
              if (entry.isIntersecting) {
                newSet.add(elementId);
              } else {
                newSet.delete(elementId);
              }
              return newSet;
            });
          }
        });
      },
      { threshold }
    );

    // Observa todos os elementos com data-animate-id
    const elements = document.querySelectorAll('[data-animate-id]');
    elements.forEach(el => observer.observe(el));

    return () => observer.disconnect();
  }, [threshold]);

  const isVisible = (elementId: string) => visibleElements.has(elementId);

  return { isVisible };
};

// ============================================================================
// HOOK PARA HOVER STATES
// ============================================================================

export const useHoverAnimation = () => {
  const [hoveredElement, setHoveredElement] = useState<string | null>(null);

  const handleMouseEnter = (elementId: string) => {
    setHoveredElement(elementId);
  };

  const handleMouseLeave = () => {
    setHoveredElement(null);
  };

  const isHovered = (elementId: string) => hoveredElement === elementId;

  return {
    hoveredElement,
    handleMouseEnter,
    handleMouseLeave,
    isHovered
  };
};

// ============================================================================
// HOOK PARA LOADING STATES ANIMADOS
// ============================================================================

export const useLoadingAnimation = (isLoading: boolean, minDuration: number = 500) => {
  const [showLoading, setShowLoading] = useState(isLoading);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isLoading) {
      setShowLoading(true);
      setIsAnimating(true);
    } else {
      // Garante duração mínima para evitar flashes
      const timer = setTimeout(() => {
        setIsAnimating(false);
        // Pequeno delay adicional para animação de saída
        setTimeout(() => setShowLoading(false), 200);
      }, minDuration);

      return () => clearTimeout(timer);
    }
  }, [isLoading, minDuration]);

  return { showLoading, isAnimating };
};

// ============================================================================
// HOOK PARA STAGGER ANIMATIONS
// ============================================================================

export const useStaggerAnimation = (itemCount: number, delay: number = 100) => {
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());

  const triggerStagger = () => {
    setVisibleItems(new Set()); // Reset
    
    for (let i = 0; i < itemCount; i++) {
      setTimeout(() => {
        setVisibleItems(prev => new Set(Array.from(prev).concat([i])));
      }, i * delay);
    }
  };

  const isItemVisible = (index: number) => visibleItems.has(index);

  return { triggerStagger, isItemVisible, visibleItems };
};

// ============================================================================
// HOOK PARA ANIMAÇÕES DE CONTADORES
// ============================================================================

export const useCounterAnimation = (
  targetValue: number,
  duration: number = 2000,
  startOnMount: boolean = true
) => {
  const [currentValue, setCurrentValue] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const startAnimation = () => {
    setIsAnimating(true);
    setCurrentValue(0);

    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      // Easing function (ease-out-quart)
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      setCurrentValue(targetValue * easeOutQuart);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      } else {
        setIsAnimating(false);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => cancelAnimationFrame(animationFrame);
  };

  useEffect(() => {
    if (startOnMount && targetValue > 0) {
      const cleanup = startAnimation();
      return cleanup;
    }
  }, [targetValue, duration, startOnMount]);

  return {
    currentValue,
    isAnimating,
    startAnimation
  };
};

// ============================================================================
// HOOK PARA MICRO-INTERAÇÕES
// ============================================================================

export const useMicroInteractions = () => {
  const [interactions, setInteractions] = useState<{
    [key: string]: {
      isPressed: boolean;
      isHovered: boolean;
      isFocused: boolean;
    }
  }>({});

  const updateInteraction = (
    elementId: string,
    type: 'pressed' | 'hovered' | 'focused',
    value: boolean
  ) => {
    setInteractions(prev => ({
      ...prev,
      [elementId]: {
        ...prev[elementId],
        [`is${type.charAt(0).toUpperCase() + type.slice(1)}`]: value
      }
    }));
  };

  const getInteractionProps = (elementId: string) => ({
    onMouseDown: () => updateInteraction(elementId, 'pressed', true),
    onMouseUp: () => updateInteraction(elementId, 'pressed', false),
    onMouseLeave: () => {
      updateInteraction(elementId, 'pressed', false);
      updateInteraction(elementId, 'hovered', false);
    },
    onMouseEnter: () => updateInteraction(elementId, 'hovered', true),
    onFocus: () => updateInteraction(elementId, 'focused', true),
    onBlur: () => updateInteraction(elementId, 'focused', false),
  });

  const getInteractionState = (elementId: string) => 
    interactions[elementId] || {
      isPressed: false,
      isHovered: false,
      isFocused: false
    };

  return {
    getInteractionProps,
    getInteractionState,
    updateInteraction
  };
};

export default usePageTransition;
