import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Divider,
  Container,
  Paper
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email,
  Lock,
  FitnessCenter
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { colors } from '../styles/colors';
import { AnimatedBox, FadeInWhenVisible } from '../components/common/AnimatedComponents';

// ============================================================================
// LOGIN PAGE - PÁGINA DE AUTENTICAÇÃO
// ============================================================================

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { login, isAuthenticated, isLoading, error, clearError } = useAuth();
  
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  // Redirecionar se já estiver autenticado
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // Limpar erro quando componente desmonta
  useEffect(() => {
    return () => clearError();
  }, [clearError]);

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
    
    // Limpar erro do campo quando usuário começar a digitar
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    if (!formData.email.trim()) {
      errors.email = 'Email é obrigatório';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email inválido';
    }

    if (!formData.password) {
      errors.password = 'Senha é obrigatória';
    } else if (formData.password.length < 6) {
      errors.password = 'Senha deve ter pelo menos 6 caracteres';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    clearError();

    if (!validateForm()) {
      return;
    }

    const success = await login(formData.email, formData.password);
    if (success) {
      navigate('/dashboard');
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSubmit(event as any);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${colors.ocean} 0%, ${colors.sea} 100%)`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2
      }}
    >
      <Container maxWidth="sm">
        <AnimatedBox animation="fadeInUp" duration={0.8}>
          <Paper
            elevation={24}
            sx={{
              borderRadius: 4,
              overflow: 'hidden',
              background: colors.cloud,
              boxShadow: `0 20px 40px ${colors.ocean}40`
            }}
          >
            {/* Header com Logo */}
            <Box
              sx={{
                background: `linear-gradient(45deg, ${colors.ocean} 30%, ${colors.sea} 90%)`,
                p: 4,
                textAlign: 'center',
                color: colors.cloud
              }}
            >
              <FadeInWhenVisible delay={0.3}>
                <Box sx={{ mb: 2 }}>
                  <img
                    src="/KVM-personal-trainer.png"
                    alt="KHATFIT"
                    style={{
                      width: 80,
                      height: 80,
                      borderRadius: '50%',
                      border: `3px solid ${colors.cloud}`,
                      boxShadow: `0 8px 16px ${colors.ocean}60`
                    }}
                    onError={(e) => {
                      // Fallback para ícone se imagem não carregar
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const fallback = document.createElement('div');
                      fallback.innerHTML = `
                        <div style="
                          width: 80px; 
                          height: 80px; 
                          background: ${colors.sea}; 
                          border-radius: 50%; 
                          display: flex; 
                          align-items: center; 
                          justify-content: center;
                          margin: 0 auto;
                          border: 3px solid ${colors.cloud};
                          box-shadow: 0 8px 16px ${colors.ocean}60;
                        ">
                          <svg width="40" height="40" fill="${colors.cloud}" viewBox="0 0 24 24">
                            <path d="M20.57 14.86L22 13.43 20.57 12 17 15.57 8.43 7 12 3.43 10.57 2 9.14 3.43 7.71 2 5.57 4.14 4.14 2.71 2.71 4.14l1.43 1.43L2 7.71l1.43 1.43L2 10.57 3.43 12 7 8.43 15.57 17 12 20.57 13.43 22l1.43-1.43L16.29 22l2.14-2.14 1.43 1.43 1.43-1.43-1.43-1.43L22 16.29l-1.43-1.43z"/>
                          </svg>
                        </div>
                      `;
                      target.parentNode?.appendChild(fallback.firstChild!);
                    }}
                  />
                </Box>
                <Typography variant="h4" fontWeight="bold" gutterBottom>
                  KHATFIT
                </Typography>
                <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
                  Sistema de Gerenciamento para Personal Trainers
                </Typography>
              </FadeInWhenVisible>
            </Box>

            {/* Formulário de Login */}
            <CardContent sx={{ p: 4 }}>
              <FadeInWhenVisible delay={0.5}>
                <Typography variant="h5" fontWeight="600" gutterBottom sx={{ color: colors.ocean, mb: 3 }}>
                  Fazer Login
                </Typography>

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <Box component="form" onSubmit={handleSubmit}>
                  <TextField
                    fullWidth
                    label="Email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    onKeyPress={handleKeyPress}
                    error={!!formErrors.email}
                    helperText={formErrors.email}
                    sx={{ mb: 3 }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Email sx={{ color: colors.sea }} />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <TextField
                    fullWidth
                    label="Senha"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleInputChange('password')}
                    onKeyPress={handleKeyPress}
                    error={!!formErrors.password}
                    helperText={formErrors.password}
                    sx={{ mb: 4 }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock sx={{ color: colors.sea }} />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={isLoading}
                    sx={{
                      py: 1.5,
                      mb: 3,
                      background: `linear-gradient(45deg, ${colors.ocean} 30%, ${colors.sea} 90%)`,
                      borderRadius: 2,
                      fontWeight: 600,
                      '&:hover': {
                        background: `linear-gradient(45deg, ${colors.sea} 30%, ${colors.ocean} 90%)`,
                        boxShadow: `0 8px 16px ${colors.sea}40`
                      }
                    }}
                  >
                    {isLoading ? 'Entrando...' : 'Entrar'}
                  </Button>

                  <Divider sx={{ my: 3 }}>
                    <Typography variant="body2" color="textSecondary">
                      Não tem uma conta?
                    </Typography>
                  </Divider>

                  <Button
                    component={Link}
                    to="/register"
                    fullWidth
                    variant="outlined"
                    size="large"
                    sx={{
                      py: 1.5,
                      borderColor: colors.sea,
                      color: colors.sea,
                      borderRadius: 2,
                      fontWeight: 600,
                      '&:hover': {
                        borderColor: colors.ocean,
                        color: colors.ocean,
                        backgroundColor: `${colors.sea}10`
                      }
                    }}
                  >
                    Criar Conta
                  </Button>
                </Box>
              </FadeInWhenVisible>
            </CardContent>
          </Paper>
        </AnimatedBox>
      </Container>
    </Box>
  );
};

export default LoginPage;

// ============================================================================
// REGISTER PAGE - PÁGINA DE CADASTRO
// ============================================================================

export const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const { register, isAuthenticated, isLoading, error, clearError } = useAuth();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    clinic_name: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  // Redirecionar se já estiver autenticado
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // Limpar erro quando componente desmonta
  useEffect(() => {
    return () => clearError();
  }, [clearError]);

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));

    // Limpar erro do campo quando usuário começar a digitar
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      errors.name = 'Nome é obrigatório';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email é obrigatório';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email inválido';
    }

    if (!formData.password) {
      errors.password = 'Senha é obrigatória';
    } else if (formData.password.length < 6) {
      errors.password = 'Senha deve ter pelo menos 6 caracteres';
    }

    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Senhas não coincidem';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    clearError();

    if (!validateForm()) {
      return;
    }

    const userData = {
      name: formData.name,
      email: formData.email,
      password: formData.password,
      phone: formData.phone || undefined,
      clinic_name: formData.clinic_name || undefined,
      role: 'trainer' as const,
      is_active: true
    };

    const success = await register(userData);
    if (success) {
      navigate('/dashboard');
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${colors.ocean} 0%, ${colors.sea} 100%)`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2
      }}
    >
      <Container maxWidth="md">
        <AnimatedBox animation="fadeInUp" duration={0.8}>
          <Paper
            elevation={24}
            sx={{
              borderRadius: 4,
              overflow: 'hidden',
              background: colors.cloud,
              boxShadow: `0 20px 40px ${colors.ocean}40`
            }}
          >
            {/* Header com Logo */}
            <Box
              sx={{
                background: `linear-gradient(45deg, ${colors.ocean} 30%, ${colors.sea} 90%)`,
                p: 4,
                textAlign: 'center',
                color: colors.cloud
              }}
            >
              <FadeInWhenVisible delay={0.3}>
                <Box sx={{ mb: 2 }}>
                  <img
                    src="/KVM-personal-trainer.png"
                    alt="KHATFIT"
                    style={{
                      width: 60,
                      height: 60,
                      borderRadius: '50%',
                      border: `3px solid ${colors.cloud}`,
                      boxShadow: `0 8px 16px ${colors.ocean}60`
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                </Box>
                <Typography variant="h4" fontWeight="bold" gutterBottom>
                  Criar Conta
                </Typography>
                <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
                  Junte-se ao KHATFIT
                </Typography>
              </FadeInWhenVisible>
            </Box>

            {/* Formulário de Registro */}
            <CardContent sx={{ p: 4 }}>
              <FadeInWhenVisible delay={0.5}>
                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <Box component="form" onSubmit={handleSubmit}>
                  <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3, mb: 3 }}>
                    <TextField
                      fullWidth
                      label="Nome Completo"
                      value={formData.name}
                      onChange={handleInputChange('name')}
                      error={!!formErrors.name}
                      helperText={formErrors.name}
                      required
                    />

                    <TextField
                      fullWidth
                      label="Email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange('email')}
                      error={!!formErrors.email}
                      helperText={formErrors.email}
                      required
                    />

                    <TextField
                      fullWidth
                      label="Telefone"
                      value={formData.phone}
                      onChange={handleInputChange('phone')}
                      placeholder="(11) 99999-9999"
                    />

                    <TextField
                      fullWidth
                      label="Nome da Academia/Estúdio"
                      value={formData.clinic_name}
                      onChange={handleInputChange('clinic_name')}
                      placeholder="Opcional"
                    />

                    <TextField
                      fullWidth
                      label="Senha"
                      type={showPassword ? 'text' : 'password'}
                      value={formData.password}
                      onChange={handleInputChange('password')}
                      error={!!formErrors.password}
                      helperText={formErrors.password}
                      required
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => setShowPassword(!showPassword)}
                              edge="end"
                            >
                              {showPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      fullWidth
                      label="Confirmar Senha"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={formData.confirmPassword}
                      onChange={handleInputChange('confirmPassword')}
                      error={!!formErrors.confirmPassword}
                      helperText={formErrors.confirmPassword}
                      required
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              edge="end"
                            >
                              {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={isLoading}
                    sx={{
                      py: 1.5,
                      mb: 3,
                      background: `linear-gradient(45deg, ${colors.ocean} 30%, ${colors.sea} 90%)`,
                      borderRadius: 2,
                      fontWeight: 600,
                      '&:hover': {
                        background: `linear-gradient(45deg, ${colors.sea} 30%, ${colors.ocean} 90%)`,
                        boxShadow: `0 8px 16px ${colors.sea}40`
                      }
                    }}
                  >
                    {isLoading ? 'Criando Conta...' : 'Criar Conta'}
                  </Button>

                  <Divider sx={{ my: 3 }}>
                    <Typography variant="body2" color="textSecondary">
                      Já tem uma conta?
                    </Typography>
                  </Divider>

                  <Button
                    component={Link}
                    to="/login"
                    fullWidth
                    variant="outlined"
                    size="large"
                    sx={{
                      py: 1.5,
                      borderColor: colors.sea,
                      color: colors.sea,
                      borderRadius: 2,
                      fontWeight: 600,
                      '&:hover': {
                        borderColor: colors.ocean,
                        color: colors.ocean,
                        backgroundColor: `${colors.sea}10`
                      }
                    }}
                  >
                    Fazer Login
                  </Button>
                </Box>
              </FadeInWhenVisible>
            </CardContent>
          </Paper>
        </AnimatedBox>
      </Container>
    </Box>
  );
};
