import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { UserModel, User } from '../models/User';

// ============================================================================
// AUTH CONTEXT - SISTEMA DE AUTENTICAÇÃO
// ============================================================================

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (userData: Omit<User, 'id' | 'created_at' | 'updated_at'>) => Promise<boolean>;
  logout: () => void;
  updateProfile: (userData: Partial<User>) => Promise<boolean>;
  error: string | null;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Verificar se há usuário logado ao inicializar
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        // Inicializar o UserModel primeiro
        await UserModel.inicializar();

        const savedUserId = localStorage.getItem('userId');
        if (savedUserId) {
          const userData = await UserModel.obterPorId(parseInt(savedUserId));
          if (userData) {
            setUser(userData);
          } else {
            localStorage.removeItem('userId');
          }
        }
      } catch (error) {
        console.error('Erro ao verificar status de autenticação:', error);
        localStorage.removeItem('userId');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      const userData = await UserModel.autenticar(email, password);
      if (userData) {
        setUser(userData);
        localStorage.setItem('userId', userData.id.toString());
        return true;
      } else {
        setError('Email ou senha incorretos');
        return false;
      }
    } catch (error) {
      console.error('Erro no login:', error);
      setError('Erro interno. Tente novamente.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: Omit<User, 'id' | 'created_at' | 'updated_at'>): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      // Verificar se email já existe
      const existingUser = await UserModel.obterPorEmail(userData.email);
      if (existingUser) {
        setError('Este email já está cadastrado');
        return false;
      }

      const userId = await UserModel.criar(userData);
      const newUser = await UserModel.obterPorId(userId);
      
      if (newUser) {
        setUser(newUser);
        localStorage.setItem('userId', newUser.id.toString());
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Erro no registro:', error);
      setError('Erro ao criar conta. Tente novamente.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('userId');
    setError(null);
  };

  const updateProfile = async (userData: Partial<User>): Promise<boolean> => {
    try {
      if (!user) return false;
      
      setIsLoading(true);
      setError(null);

      const success = await UserModel.atualizar(user.id, userData);
      if (success) {
        const updatedUser = await UserModel.obterPorId(user.id);
        if (updatedUser) {
          setUser(updatedUser);
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error);
      setError('Erro ao atualizar perfil. Tente novamente.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    updateProfile,
    error,
    clearError
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
