import { getDatabase } from './db';
import { pathUtils } from '../utils/path';

// Verificar se estamos no Electron
const isElectron = typeof window !== 'undefined' &&
                  window.navigator &&
                  /electron/i.test(window.navigator.userAgent);

// Importação condicional do better-sqlite3 (apenas no Electron)
let Database: any = null;
if (isElectron) {
  try {
    // eslint-disable-next-line
    Database = require('better-sqlite3');
  } catch (error) {
    console.error('Erro ao importar better-sqlite3:', error);
  }
}

const isDevelopment = process.env.NODE_ENV === 'development';

// Importação condicional para evitar erros no navegador
let app: any = null;
if (isElectron) {
  try {
    // eslint-disable-next-line
    const electron = require('electron');
    app = electron.app;
  } catch (error) {
    console.log('Electron não disponível');
  }
}

// Determina o caminho do banco de dados (apenas para Electron)
export const getDbPath = () => {
  const userDataPath = isDevelopment ? '.' : (app ? app.getPath('userData') : '.');
  return pathUtils.join(userDataPath, 'database.sqlite');
};

export const initDatabase = async () => {
  try {
    console.log('Inicializando banco de dados...');
    const db = getDatabase();

    if (isElectron && Database) {
      // SQLite para Electron
      console.log('Inicializando SQLite (Electron)...');

      // Habilita as foreign keys
      db.pragma('foreign_keys = ON');

      // Cria a tabela de usuários
      db.exec(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          email TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          avatar TEXT,
          phone TEXT,
          clinic_name TEXT,
          clinic_address TEXT,
          clinic_phone TEXT,
          role TEXT DEFAULT 'trainer' CHECK (role IN ('admin', 'trainer', 'user')),
          is_active BOOLEAN DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Cria índices para usuários
      db.exec('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)');
      db.exec('CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)');

      // Verificar se existe algum usuário, se não, criar admin padrão
      const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get() as { count: number };
      if (userCount.count === 0) {
        console.log('Criando usuário administrador padrão...');
        // Função simples de hash
        const simpleHash = (password: string): string => {
          return btoa(password + 'salt');
        };

        const hashedPassword = simpleHash('admin123');

        db.prepare(`
          INSERT INTO users (name, email, password, role, clinic_name, is_active)
          VALUES (?, ?, ?, ?, ?, ?)
        `).run(
          'Administrador',
          '<EMAIL>',
          hashedPassword,
          'admin',
          'Hyper Personal Training',
          1
        );
        console.log('Usuário administrador criado: <EMAIL> / admin123');
      }

      // Cria a tabela de clientes
      db.exec(`
        CREATE TABLE IF NOT EXISTS clientes (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          nome TEXT NOT NULL,
          data_nascimento TEXT,
          sexo TEXT,
          email TEXT,
          telefone TEXT,
          observacoes TEXT,
          data_cadastro TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Cria a tabela de avaliações físicas
      db.exec(`
        CREATE TABLE IF NOT EXISTS avaliacoes_fisicas (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          cliente_id INTEGER NOT NULL,
          data_avaliacao TEXT NOT NULL,
          peso REAL NOT NULL,
          altura REAL NOT NULL,
          idade INTEGER,
          FOREIGN KEY (cliente_id) REFERENCES clientes (id) ON DELETE CASCADE
        )
      `);

      // Cria a tabela de dobras cutâneas
      db.exec(`
        CREATE TABLE IF NOT EXISTS dobras_cutaneas (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          avaliacao_id INTEGER NOT NULL,
          subescapular REAL,
          tricipital REAL,
          bicipital REAL,
          axilar_media REAL,
          suprailiaca REAL,
          abdominal REAL,
          coxa REAL,
          panturrilha REAL,
          percentual_gordura REAL,
          FOREIGN KEY (avaliacao_id) REFERENCES avaliacoes_fisicas (id) ON DELETE CASCADE
        )
      `);

      // Cria a tabela de medidas antropométricas
      db.exec(`
        CREATE TABLE IF NOT EXISTS medidas_antropometricas (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          avaliacao_id INTEGER NOT NULL,
          braco_direito REAL,
          braco_esquerdo REAL,
          antebraco_direito REAL,
          antebraco_esquerdo REAL,
          torax REAL,
          cintura REAL,
          abdomen REAL,
          quadril REAL,
          coxa_direita REAL,
          coxa_esquerda REAL,
          panturrilha_direita REAL,
          panturrilha_esquerda REAL,
          FOREIGN KEY (avaliacao_id) REFERENCES avaliacoes_fisicas (id) ON DELETE CASCADE
        )
      `);

      // Cria a tabela de treinos
      db.exec(`
        CREATE TABLE IF NOT EXISTS treinos (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          cliente_id INTEGER NOT NULL,
          nome TEXT NOT NULL,
          data_inicio TEXT NOT NULL,
          data_fim TEXT,
          observacoes TEXT,
          data_criacao TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (cliente_id) REFERENCES clientes (id) ON DELETE CASCADE
        )
      `);

      // Cria a tabela de exercícios
      db.exec(`
        CREATE TABLE IF NOT EXISTS exercicios (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          treino_id INTEGER NOT NULL,
          nome TEXT NOT NULL,
          tipo_treino TEXT NOT NULL,
          ordem INTEGER NOT NULL,
          FOREIGN KEY (treino_id) REFERENCES treinos (id) ON DELETE CASCADE
        )
      `);

      // Cria a tabela de séries
      db.exec(`
        CREATE TABLE IF NOT EXISTS series (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          exercicio_id INTEGER NOT NULL,
          semana INTEGER NOT NULL,
          numero_serie INTEGER NOT NULL,
          repeticoes INTEGER NOT NULL,
          carga REAL NOT NULL,
          volume_carga REAL NOT NULL,
          FOREIGN KEY (exercicio_id) REFERENCES exercicios (id) ON DELETE CASCADE
        )
      `);
    } else {
      // IndexedDB para navegador
      console.log('Inicializando IndexedDB (Navegador)...');

      // O IndexedDB é inicializado automaticamente na classe BrowserDatabase
      // As tabelas são criadas no evento onupgradeneeded
      await db.init();
    }

    console.log('Banco de dados inicializado com sucesso!');
    return db;
  } catch (error) {
    console.error('Erro ao inicializar o banco de dados:', error);
    throw error;
  }
};

// Função de inicialização completa
export const initializeDatabase = async () => {
  try {
    console.log('Inicializando sistema de banco de dados...');
    await initDatabase();

    // Aguardar um pouco para garantir que o banco está pronto
    await new Promise(resolve => setTimeout(resolve, 100));

    console.log('Sistema de banco de dados inicializado com sucesso!');
  } catch (error) {
    console.error('Erro ao inicializar sistema de banco de dados:', error);
  }
};