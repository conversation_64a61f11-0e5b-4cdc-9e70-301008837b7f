import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Tooltip,
  IconButton,
  Stack
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import PeopleIcon from '@mui/icons-material/People';
import AssessmentIcon from '@mui/icons-material/Assessment';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import BarChartIcon from '@mui/icons-material/BarChart';

import InfoIcon from '@mui/icons-material/Info';
import { useAppContext } from '../contexts/AppContext';
import { useAvaliacaoFisica } from '../contexts/AvaliacaoFisicaContext';
import { useTreino } from '../contexts/TreinoContext';
import { Cliente } from '../models/Cliente';
import { AvaliacaoFisica } from '../models/AvaliacaoFisica';
import { Treino } from '../models/Treino';
import { EmptyState } from '../components/common/EmptyState';
import { StatsCard } from '../components/common/StatsCard';
import { PageContainer, ContentCard, SectionDivider } from '../components/common/PageContainer';
import {
  AnimatedBox,
  AnimatedCard,
  FadeInWhenVisible,
  AnimatedList
} from '../components/common/AnimatedComponents';

import { colors } from '../styles/colors';

interface Estatisticas {
  totalClientes: number;
  totalAvaliacoes: number;
  totalTreinos: number;
  clientesRecentes: Cliente[];
  avaliacoesRecentes: AvaliacaoFisica[];
  treinosRecentes: Treino[];
}

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const { clientes, buscarClientes } = useAppContext();
  const { avaliacoes, carregarAvaliacoes } = useAvaliacaoFisica();
  const { treinos } = useTreino();
  
  const [estatisticas, setEstatisticas] = useState<Estatisticas>({
    totalClientes: 0,
    totalAvaliacoes: 0,
    totalTreinos: 0,
    clientesRecentes: [],
    avaliacoesRecentes: [],
    treinosRecentes: []
  });

  useEffect(() => {
    const carregarDados = async () => {
      await buscarClientes();
      await carregarAvaliacoes();
    };
    
    carregarDados();
  }, [buscarClientes, carregarAvaliacoes]);

  useEffect(() => {
    setEstatisticas({
      totalClientes: clientes.length,
      totalAvaliacoes: avaliacoes.length,
      totalTreinos: treinos.length,
      clientesRecentes: clientes.slice(0, 5),
      avaliacoesRecentes: avaliacoes.slice(0, 5),
      treinosRecentes: treinos.slice(0, 5)
    });
  }, [clientes, avaliacoes, treinos]);

  const cards = [
    {
      title: 'Clientes',
      icon: <PeopleIcon fontSize="large" />,
      count: estatisticas.totalClientes,
      action: () => navigate('/clientes'),
      buttonText: 'Gerenciar Clientes',
      color: colors.sea,
      description: 'Total de clientes ativos cadastrados no sistema'
    },
    {
      title: 'Avaliações Físicas',
      icon: <AssessmentIcon fontSize="large" />,
      count: estatisticas.totalAvaliacoes,
      action: () => navigate('/avaliacoes'),
      buttonText: 'Gerenciar Avaliações',
      color: colors.sea,
      description: 'Avaliações físicas realizadas'
    },
    {
      title: 'Treinos',
      icon: <FitnessCenterIcon fontSize="large" />,
      count: estatisticas.totalTreinos,
      action: () => navigate('/treinos'),
      buttonText: 'Gerenciar Treinos',
      color: colors.sea,
      description: 'Planilhas de treino ativas'
    }
  ];

  return (
    <PageContainer>
      <AnimatedBox animation="fadeInUp" duration={0.8}>
        <Box sx={{ mb: 4, textAlign: 'center' }}>
          <Typography variant="h3" component="h1" gutterBottom sx={{
            fontWeight: 700,
            color: colors.ocean,
            background: `linear-gradient(45deg, ${colors.ocean}, ${colors.sea})`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            Bem-vindo ao KHATFIT
          </Typography>
          <Typography variant="h6" color="textSecondary" sx={{ maxWidth: 600, mx: 'auto' }}>
            Sistema completo de gerenciamento para Personal Trainers. Gerencie seus clientes, avaliações físicas e treinos em um só lugar.
          </Typography>
        </Box>
      </AnimatedBox>

      <AnimatedList staggerDelay={0.1}>
        <Grid container spacing={{ xs: 2, sm: 3 }}>
          {cards.map((card, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <StatsCard
                title={card.title}
                value={card.count}
                icon={card.icon}
                color={card.color}
                description={card.description}
                onClick={card.action}
                delay={index * 200}
              />
            </Grid>
          ))}
        </Grid>
      </AnimatedList>

      <SectionDivider />

      <Grid container spacing={{ xs: 2, sm: 3 }}>
        <Grid item xs={12} md={6}>
          <FadeInWhenVisible delay={0.3}>
            <AnimatedCard hoverElevation={true} glowColor={colors.professional[500]}>
              <ContentCard padding="medium" hover={true}>
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
              <Box sx={{ 
                bgcolor: `${colors.sea}1A`, 
                p: 1, 
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <PersonAddIcon sx={{ color: colors.sea }} />
              </Box>
              <Typography 
                variant="h6" 
                sx={{
                  fontWeight: 500,
                  color: colors.ocean,
                  fontSize: '1.1rem'
                }}
              >
                Clientes Recentes
              </Typography>
              <Box sx={{ flexGrow: 1 }} />
              <Tooltip title="Últimos 5 clientes cadastrados" arrow>
                <IconButton size="small" sx={{ bgcolor: `${colors.ocean}0A`, borderRadius: '50%' }}>
                  <InfoIcon fontSize="small" sx={{ color: `${colors.ocean}80` }} />
                </IconButton>
              </Tooltip>
            </Stack>
            <Divider sx={{ mb: 2, borderColor: `${colors.ocean}1A` }} />
            <List sx={{
              maxHeight: '300px',
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: `${colors.sea}4D`,
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: colors.sea,
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: colors.ocean,
              }
            }}>
              {estatisticas.clientesRecentes.length > 0 ? (
                estatisticas.clientesRecentes.map((cliente: any) => (
                  <ListItem 
                    key={cliente.id}
                    sx={{
                      borderRadius: 3,
                      mb: 1,
                      '&:hover': {
                        bgcolor: `${colors.sea}0A`
                      }
                    }}
                  >
                    <ListItemIcon>
                      <Box sx={{ 
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        bgcolor: `${colors.sea}1A`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: colors.sea
                      }}>
                        <PeopleIcon />
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="subtitle1" sx={{ color: colors.ocean, fontWeight: 500 }}>
                          {cliente.nome}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                          {cliente.email}
                        </Typography>
                      }
                    />
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => navigate(`/clientes/${cliente.id}`)}
                      sx={{
                        borderColor: colors.sea,
                        color: colors.sea,
                        '&:hover': {
                          borderColor: colors.ocean,
                          color: colors.ocean,
                          bgcolor: `${colors.sea}0A`
                        }
                      }}
                    >
                      Ver Detalhes
                    </Button>
                  </ListItem>
                ))
              ) : (
                <Box sx={{ py: 2 }}>
                  <EmptyState
                    variant="clientes"
                    title="Nenhum cliente cadastrado"
                    description="Adicione seu primeiro cliente para começar a usar o sistema."
                    actionLabel="Adicionar Cliente"
                    onAction={() => navigate('/clientes')}
                    size="small"
                  />
                </Box>
              )}
            </List>
          </ContentCard>
        </AnimatedCard>
      </FadeInWhenVisible>
    </Grid>

        <Grid item xs={12} md={6}>
          <FadeInWhenVisible delay={0.5}>
            <AnimatedCard hoverElevation={true} glowColor={colors.health[500]}>
              <ContentCard padding="medium" hover={true}>
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
              <Box sx={{ 
                bgcolor: `${colors.sea}1A`, 
                p: 1, 
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <AssessmentIcon sx={{ color: colors.sea }} />
              </Box>
              <Typography 
                variant="h6" 
                sx={{
                  fontWeight: 500,
                  color: colors.ocean,
                  fontSize: '1.1rem'
                }}
              >
                Últimas Avaliações
              </Typography>
              <Box sx={{ flexGrow: 1 }} />
              <Tooltip title="Últimas 5 avaliações realizadas" arrow>
                <IconButton size="small" sx={{ bgcolor: `${colors.ocean}0A`, borderRadius: '50%' }}>
                  <InfoIcon fontSize="small" sx={{ color: `${colors.ocean}80` }} />
                </IconButton>
              </Tooltip>
            </Stack>
            <Divider sx={{ mb: 2, borderColor: `${colors.ocean}1A` }} />
            <List sx={{
              maxHeight: '300px',
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: `${colors.sea}4D`,
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: colors.sea,
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: colors.ocean,
              }
            }}>
              {estatisticas.avaliacoesRecentes.length > 0 ? (
                estatisticas.avaliacoesRecentes.map((avaliacao: any) => (
                  <ListItem 
                    key={avaliacao.id}
                    sx={{
                      borderRadius: 3,
                      mb: 1,
                      '&:hover': {
                        bgcolor: `${colors.sea}0A`
                      }
                    }}
                  >
                    <ListItemIcon>
                      <Box sx={{ 
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        bgcolor: `${colors.sea}1A`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: colors.sea
                      }}>
                        <BarChartIcon />
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="subtitle1" sx={{ color: colors.ocean, fontWeight: 500 }}>
                          {avaliacao.cliente?.nome || 'Cliente não encontrado'}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                          {new Date(avaliacao.data).toLocaleDateString()}
                        </Typography>
                      }
                    />
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => navigate(`/avaliacoes/${avaliacao.id}`)}
                      sx={{
                        borderColor: colors.sea,
                        color: colors.sea,
                        '&:hover': {
                          borderColor: colors.ocean,
                          color: colors.ocean,
                          bgcolor: `${colors.sea}0A`
                        }
                      }}
                    >
                      Ver Detalhes
                    </Button>
                  </ListItem>
                ))
              ) : (
                <Box sx={{ py: 2 }}>
                  <EmptyState
                    variant="avaliacoes"
                    title="Nenhuma avaliação realizada"
                    description="Realize a primeira avaliação física para acompanhar a evolução dos clientes."
                    actionLabel="Nova Avaliação"
                    onAction={() => navigate('/avaliacoes')}
                    size="small"
                  />
                </Box>
              )}
            </List>
              </ContentCard>
            </AnimatedCard>
          </FadeInWhenVisible>
        </Grid>
      </Grid>

    </PageContainer>
  );
};

export default HomePage; 