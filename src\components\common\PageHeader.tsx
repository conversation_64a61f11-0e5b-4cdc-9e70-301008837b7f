import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { colors } from '../../styles/colors';

// ============================================================================
// PAGE HEADER - CABEÇALHO PADRONIZADO PARA PÁGINAS
// ============================================================================

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  actionButton?: {
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
    disabled?: boolean;
  };
  children?: React.ReactNode;
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  actionButton,
  children
}) => {
  return (
    <Box sx={{ 
      mb: { xs: 2, sm: 3 }, 
      p: { xs: 2, sm: 3 }, 
      borderRadius: 3, 
      background: `linear-gradient(45deg, ${colors.ocean} 30%, ${colors.sea} 90%)`,
      display: 'flex',
      flexDirection: { xs: 'column', sm: 'row' },
      alignItems: { xs: 'flex-start', sm: 'center' },
      gap: { xs: 2, sm: 0 },
      justifyContent: 'space-between'
    }}>
      <Box sx={{ textAlign: { xs: 'center', sm: 'left' }, width: { xs: '100%', sm: 'auto' } }}>
        <Typography variant="h4" fontWeight="bold" sx={{ 
          color: colors.cloud,
          fontSize: { xs: '1.5rem', sm: '2rem' },
          mb: subtitle ? 0.5 : 0
        }}>
          {title}
        </Typography>
        {subtitle && (
          <Typography 
            variant="subtitle1"
            sx={{ 
              color: `${colors.cloud}CC`,
              fontSize: { xs: '0.875rem', sm: '1rem' }
            }}
          >
            {subtitle}
          </Typography>
        )}
      </Box>

      {(actionButton || children) && (
        <Box sx={{ 
          display: 'flex', 
          gap: 2, 
          alignItems: 'center',
          width: { xs: '100%', sm: 'auto' },
          justifyContent: { xs: 'center', sm: 'flex-end' }
        }}>
          {children}
          {actionButton && (
            <Button
              variant="contained"
              startIcon={actionButton.icon}
              onClick={actionButton.onClick}
              disabled={actionButton.disabled}
              sx={{
                bgcolor: colors.cloud,
                color: colors.ocean,
                borderRadius: 2,
                py: 1,
                px: 2,
                '&:hover': {
                  bgcolor: colors.sea,
                  color: colors.cloud,
                  boxShadow: `0px 4px 8px ${colors.ocean}33`
                },
                '&.Mui-disabled': {
                  bgcolor: `${colors.cloud}4D`,
                  color: `${colors.ocean}4D`
                }
              }}
            >
              {actionButton.label}
            </Button>
          )}
        </Box>
      )}
    </Box>
  );
};

// ============================================================================
// SIMPLE PAGE HEADER - VERSÃO SIMPLIFICADA PARA PÁGINAS MENORES
// ============================================================================

interface SimplePageHeaderProps {
  title: string;
  subtitle?: string;
  centered?: boolean;
}

export const SimplePageHeader: React.FC<SimplePageHeaderProps> = ({
  title,
  subtitle,
  centered = false
}) => {
  return (
    <Box sx={{
      mb: { xs: 2, sm: 3 },
      textAlign: centered ? 'center' : 'left',
      background: `${colors.sea}0A`,
      borderRadius: 3,
      p: { xs: 2, sm: 3 }
    }}>
      <Typography
        variant="h3"
        gutterBottom
        fontWeight="bold"
        sx={{
          color: colors.ocean,
          fontSize: { xs: '1.5rem', sm: '2rem' }
        }}
      >
        {title}
      </Typography>
      {subtitle && (
        <Typography
          variant="subtitle1"
          sx={{
            color: `${colors.ocean}99`,
            fontSize: { xs: '0.875rem', sm: '1rem' }
          }}
        >
          {subtitle}
        </Typography>
      )}
    </Box>
  );
};

export default PageHeader;
