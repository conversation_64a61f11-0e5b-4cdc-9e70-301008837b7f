<div align="center">
  <img src="public/KVM-personal-trainer.png" alt="KHATFIT - Sistema Completo" width="220"/>

  <h1>🏋️‍♂️ KHATFIT</h1>
  <h3>Sistema Completo de Gerenciamento para Personal Trainers</h3>

  <p>
    <img src="https://img.shields.io/badge/Status-100%25%20Completo-success" alt="Status: 100% Completo"/>
    <img src="https://img.shields.io/badge/Versão-2.0.0-blue" alt="Versão: 2.0.0"/>
    <img src="https://img.shields.io/badge/Licença-MIT-yellow" alt="Licença: MIT"/>
    <img src="https://img.shields.io/badge/React-18.2.0-61DAFB?logo=react" alt="React 18.2.0"/>
    <img src="https://img.shields.io/badge/TypeScript-5.0-3178C6?logo=typescript" alt="TypeScript 5.0"/>
    <img src="https://img.shields.io/badge/Material--UI-5.0-0081CB?logo=mui" alt="Material-UI 5.0"/>
  </p>

  <p><strong>Sistema web completo e profissional para personal trainers gerenciarem clientes, avaliações físicas científicas e treinos personalizados.</strong></p>

  <p>
    <strong>🎯 Pronto para Produção</strong> •
    <strong>🔐 Sistema de Autenticação</strong> •
    <strong>📊 Cálculos Científicos</strong> •
    <strong>🎨 Interface Premium</strong>
  </p>
</div>

<p align="center">
  <a href="#-principais-funcionalidades">Funcionalidades</a> •
  <a href="#-demonstração">Demonstração</a> •
  <a href="#-tecnologias">Tecnologias</a> •
  <a href="#-instalação-e-uso">Instalação</a> •
  <a href="#-estrutura-do-projeto">Estrutura</a> •
  <a href="#-funcionalidades-implementadas">Status</a> •
  <a href="#-licença">Licença</a>
</p>

---

## 🚀 Principais Funcionalidades

### 🔐 **Sistema de Autenticação Completo**
- **Login e Registro**: Interface moderna com validação em tempo real
- **Segurança**: Hash de senhas e proteção de rotas
- **Gestão de Usuários**: Sistema multi-usuário com diferentes perfis
- **Sessão Persistente**: Mantenha-se logado entre sessões

### 👥 **Gestão Profissional de Clientes**
- **CRUD Completo**: Cadastro, edição, visualização e exclusão
- **Busca Inteligente**: Sistema de busca global em tempo real
- **Filtros Avançados**: Filtros visuais com chips interativos
- **Histórico Completo**: Acompanhamento de toda evolução do cliente

### 📋 **Avaliações Físicas Científicas**
- **Protocolo Jackson & Pollock**: Cálculo de percentual de gordura (7 dobras)
- **Medidas Antropométricas**: 12 circunferências corporais
- **TMB e GET**: Taxa Metabólica Basal e Gasto Energético Total
- **Gráficos de Evolução**: Visualização temporal da progressão
- **Comparação de Avaliações**: Análise entre diferentes períodos

### 🏋️ **Sistema de Treinos Profissional**
- **Treinos A-F**: Criação de até 6 treinos diferentes
- **Biblioteca de Exercícios**: Exercícios personalizáveis
- **Volume de Carga**: Cálculo automático de séries × repetições × carga
- **Progressão Semanal**: Acompanhamento da evolução de cargas
- **Histórico Detalhado**: Evolução por exercício e período

### 📊 **Dashboard e Analytics**
- **Métricas em Tempo Real**: Estatísticas de clientes e atividades
- **Gráficos Interativos**: Visualização de dados com Chart.js
- **Relatórios PDF**: Exportação profissional de avaliações e treinos
- **Sistema de Backup**: Backup completo e restauração de dados

### 🎨 **Interface Premium**
- **Design System Profissional**: Paleta Personal Trainer (laranja, azul, verde)
- **Modo Escuro**: Tema escuro completo
- **Modo Compacto**: Interface eficiente como padrão
- **Responsivo**: Funciona perfeitamente em desktop e mobile
- **Micro-animações**: Transições suaves e feedback visual

## 🎯 Demonstração

### 🔐 **Acesso ao Sistema**
Para testar o sistema, use as credenciais padrão:
- **Email**: `<EMAIL>`
- **Senha**: `admin123`


#### 🏠 **Dashboard Principal**
- **Métricas em Tempo Real**: Visão geral de clientes ativos, avaliações recentes
- **Gráficos Interativos**: Estatísticas de evolução e progresso
- **Navegação Rápida**: Acesso direto às principais funcionalidades
- **Notificações**: Centro de notificações com lembretes importantes

#### 👥 **Gestão de Clientes**
- **Interface Moderna**: Cards visuais com informações essenciais
- **Busca Global**: Sistema de busca inteligente em tempo real
- **Filtros Avançados**: Chips visuais para filtrar por critérios específicos
- **Formulários Validados**: Cadastro com validação em tempo real

#### 📋 **Avaliações Físicas**
- **Protocolo Científico**: Jackson & Pollock para cálculo de gordura corporal
- **Interface Intuitiva**: Formulários organizados por seções
- **Cálculos Automáticos**: TMB, GET e percentual de gordura em tempo real
- **Gráficos de Evolução**: Visualização temporal da progressão

#### 🏋️ **Sistema de Treinos**
- **Treinos A-F**: Interface em abas para diferentes treinos
- **Biblioteca de Exercícios**: Exercícios personalizáveis e organizados
- **Volume de Carga**: Cálculo automático e acompanhamento
- **Progressão Visual**: Gráficos de evolução de cargas

#### ⚙️ **Configurações Avançadas**
- **Personalização Completa**: Tema, fonte, modo compacto
- **Backup e Restore**: Sistema completo de gerenciamento de dados
- **Notificações**: Configuração de lembretes e alertas
- **Informações da Clínica**: Personalização da marca


## 🛠️ Tecnologias

### **Stack Principal**
<table>
  <tr>
    <td align="center"><b>Frontend</b></td>
    <td>React 18.2.0 + TypeScript 5.0 + Material-UI 5.0</td>
  </tr>
  <tr>
    <td align="center"><b>Banco de Dados</b></td>
    <td>IndexedDB (navegador nativo)</td>
  </tr>
  <tr>
    <td align="center"><b>Gráficos</b></td>
    <td>Chart.js + React-Chartjs-2</td>
  </tr>
  <tr>
    <td align="center"><b>Roteamento</b></td>
    <td>React Router 6</td>
  </tr>
  <tr>
    <td align="center"><b>Estado</b></td>
    <td>Context API + React Hooks</td>
  </tr>
  <tr>
    <td align="center"><b>Build</b></td>
    <td>Create React App + TypeScript</td>
  </tr>
</table>

### **Funcionalidades Avançadas**
- **🔐 Autenticação**: Sistema completo com hash de senhas
- **📊 Analytics**: Métricas em tempo real com persistência
- **🔍 Busca**: Sistema de busca global inteligente
- **⌨️ Atalhos**: Navegação por teclado completa
- **🔔 Notificações**: Sistema push do navegador
- **💾 Backup**: Exportação/importação completa de dados
- **📄 PDF**: Geração de relatórios profissionais

## 💻 Requisitos do Sistema

### **Navegadores Suportados**
- **Chrome**: 90+ (recomendado)
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

### **Requisitos Mínimos**
- **RAM**: 4GB (8GB recomendado)
- **Armazenamento**: 100MB para dados do sistema
- **Conexão**: Não requer internet (funciona offline)
- **Resolução**: 1366x768 (1920x1080 recomendado)

## 🚀 Instalação e Uso

### 🌐 **Acesso Direto (Recomendado)**
O sistema está disponível como aplicação web. Basta acessar no navegador:

```
http://localhost:3000
```

**Credenciais de Teste:**
- **Email**: `<EMAIL>`
- **Senha**: `admin123`

### 💻 **Instalação para Desenvolvimento**

<details>
<summary><b>🔧 Configuração do Ambiente</b></summary>
<br>

**Pré-requisitos:**
- Node.js 16+
- npm ou yarn
- Git

**Passos:**

1. **Clone o repositório:**
   ```bash
   git clone https://github.com/seu-usuario/khatfit-personal-training.git
   cd khatfit-personal-training
   ```

2. **Instale as dependências:**
   ```bash
   npm install
   # ou
   yarn install
   ```

3. **Execute o sistema:**
   ```bash
   npm start
   # ou
   yarn start
   ```

4. **Acesse no navegador:**
   ```
   http://localhost:3000
   ```

</details>

### 🏗️ **Build para Produção**

<details>
<summary><b>📦 Gerar Build Otimizado</b></summary>
<br>

```bash
# Gerar build otimizado
npm run build

# Servir build localmente para teste
npm install -g serve
serve -s build
```

O build será gerado na pasta `build/` pronto para deploy em qualquer servidor web.

</details>

## 📁 Estrutura do Projeto

```
khatfit-personal-training/
├── 📁 public/                    # Arquivos públicos
│   ├── index.html               # Template HTML principal
│   ├── manifest.json            # Configuração PWA
│   └── favicon.ico              # Ícone da aplicação
├── 📁 src/                       # Código-fonte principal
│   ├── 📁 components/            # Componentes React reutilizáveis
│   │   ├── 📁 AvaliacaoFisica/   # Componentes de avaliação física
│   │   ├── 📁 Cliente/           # Componentes de gestão de clientes
│   │   ├── 📁 Layout/            # Componentes de layout (Header, Sidebar)
│   │   ├── 📁 Treino/            # Componentes de treinos
│   │   └── 📁 Common/            # Componentes comuns (Loading, Empty States)
│   ├── 📁 contexts/              # Contextos React (Estado Global)
│   │   ├── AuthContext.tsx      # Contexto de autenticação
│   │   ├── SettingsContext.tsx  # Contexto de configurações
│   │   └── NotificationContext.tsx # Contexto de notificações
│   ├── 📁 db/                    # Camada de banco de dados
│   │   ├── db.ts                # Configuração IndexedDB
│   │   └── init.ts              # Inicialização do banco
│   ├── 📁 models/                # Modelos de dados
│   │   ├── Cliente.ts           # Modelo de cliente
│   │   ├── AvaliacaoFisica.ts   # Modelo de avaliação física
│   │   ├── Treino.ts            # Modelo de treino
│   │   └── User.ts              # Modelo de usuário
│   ├── 📁 pages/                 # Páginas da aplicação
│   │   ├── HomePage.tsx         # Dashboard principal
│   │   ├── ClientesPage.tsx     # Gestão de clientes
│   │   ├── AvaliacoesPage.tsx   # Avaliações físicas
│   │   ├── TreinosPage.tsx      # Sistema de treinos
│   │   ├── LoginPage.tsx        # Página de login
│   │   └── RegisterPage.tsx     # Página de registro
│   ├── 📁 hooks/                 # Hooks customizados
│   │   ├── useTheme.ts          # Hook de tema
│   │   └── useKeyboardShortcuts.ts # Hook de atalhos
│   ├── 📁 utils/                 # Funções utilitárias
│   │   ├── calculations.ts      # Cálculos científicos
│   │   ├── formatters.ts        # Formatação de dados
│   │   └── validators.ts        # Validações
│   └── 📁 types/                 # Definições TypeScript
│       ├── Cliente.ts           # Tipos de cliente
│       ├── AvaliacaoFisica.ts   # Tipos de avaliação
│       └── Treino.ts            # Tipos de treino
├── 📁 assets/                    # Assets estáticos
│   └── 📁 img/LOGOSPNG/         # Logos e imagens
├── 📄 package.json              # Dependências e scripts
├── 📄 tsconfig.json             # Configuração TypeScript
├── 📄 checklist.md              # Checklist de funcionalidades
└── 📄 README.md                 # Este arquivo
```

## ✨ Funcionalidades Implementadas

### 🔐 **Sistema de Autenticação**
- [x] **Login e Registro**: Interface moderna com validação
- [x] **Hash de Senhas**: Segurança implementada
- [x] **Rotas Protegidas**: Controle de acesso por página
- [x] **Sessão Persistente**: Mantenha-se logado
- [x] **Multi-usuário**: Sistema preparado para múltiplos usuários

### 👥 **Gestão de Clientes (100%)**
- [x] **CRUD Completo**: Criar, visualizar, editar e excluir
- [x] **Busca Global**: Sistema de busca em tempo real
- [x] **Filtros Avançados**: Filtros visuais com chips
- [x] **Validação**: Formulários com validação em tempo real
- [x] **Histórico**: Acompanhamento completo da evolução

### 📋 **Avaliações Físicas (100%)**
- [x] **Protocolo Jackson & Pollock**: 7 dobras cutâneas
- [x] **Medidas Antropométricas**: 12 circunferências
- [x] **Cálculos Automáticos**: % gordura, TMB, GET
- [x] **Gráficos de Evolução**: Visualização temporal
- [x] **Comparação**: Análise entre avaliações
- [x] **Exportação PDF**: Relatórios profissionais

### 🏋️ **Sistema de Treinos (100%)**
- [x] **Treinos A-F**: Até 6 treinos diferentes
- [x] **Biblioteca de Exercícios**: Exercícios personalizáveis
- [x] **Volume de Carga**: Cálculo automático
- [x] **Progressão**: Acompanhamento de evolução
- [x] **Histórico Detalhado**: Por exercício e período

### 📊 **Dashboard e Relatórios (100%)**
- [x] **Métricas em Tempo Real**: Estatísticas atualizadas
- [x] **Gráficos Interativos**: Chart.js integrado
- [x] **Analytics**: Sistema de métricas avançadas
- [x] **Exportação**: Backup completo de dados
- [x] **Relatórios PDF**: Geração profissional

### 🎨 **Interface e UX (100%)**
- [x] **Design System**: Paleta profissional
- [x] **Modo Escuro**: Tema escuro completo
- [x] **Modo Compacto**: Interface eficiente (padrão)
- [x] **Responsivo**: Desktop e mobile
- [x] **Atalhos de Teclado**: Navegação completa
- [x] **Notificações**: Sistema push integrado
- [x] **Configurações**: Personalização completa

## 🎯 Atalhos de Teclado

O sistema possui um conjunto completo de atalhos para navegação rápida:

### **Navegação Principal**
- `Ctrl + 1` - Dashboard
- `Ctrl + 2` - Clientes
- `Ctrl + 3` - Avaliações
- `Ctrl + 4` - Treinos

### **Funcionalidades**
- `Ctrl + K` ou `/` - Busca global
- `Ctrl + ,` - Configurações
- `Ctrl + Shift + D` - Alternar tema escuro
- `?` ou `F1` - Ajuda de atalhos

### **Formulários**
- `Ctrl + S` - Salvar
- `Escape` - Cancelar/Fechar
- `Enter` - Confirmar ação

## 🔔 Sistema de Notificações

### **Tipos de Notificações**
- **📅 Lembretes**: Avaliações agendadas, aniversários
- **📊 Progresso**: Metas atingidas, marcos importantes
- **⚠️ Alertas**: Dados pendentes, atualizações necessárias
- **✅ Confirmações**: Ações realizadas com sucesso

### **Configurações**
- Notificações do navegador (quando permitido)
- Centro de notificações integrado
- Configuração de frequência e tipos
- Histórico de notificações

## 📊 Cálculos Científicos Implementados

### **Composição Corporal**
- **Protocolo Jackson & Pollock**: 7 dobras cutâneas
- **Densidade Corporal**: Cálculo preciso por gênero
- **Percentual de Gordura**: Fórmulas específicas para homens e mulheres
- **Massa Magra**: Cálculo automático baseado na composição

### **Metabolismo**
- **TMB (Taxa Metabólica Basal)**: Equações de Harris-Benedict
- **GET (Gasto Energético Total)**: Ajuste por nível de atividade
- **Recomendações Calóricas**: Baseadas em objetivos específicos

### **Medidas Antropométricas**
- **12 Circunferências**: Pescoço, braço, antebraço, tórax, cintura, quadril, coxa, panturrilha
- **Índices Corporais**: IMC, RCQ (Relação Cintura-Quadril)
- **Acompanhamento Temporal**: Evolução de todas as medidas

## 🎨 Design System

### **Paleta de Cores**
- **🟠 Laranja Energético** (#FF6B35): Motivação e energia
- **🔵 Azul Profissional** (#2C3E50): Confiança e estabilidade
- **🟢 Verde Saúde** (#27AE60): Bem-estar e crescimento
- **⚫ Modo Escuro**: Tema completo para uso noturno

### **Tipografia**
- **Fonte Principal**: Roboto (Google Fonts)
- **Tamanhos**: Small, Medium, Large (configurável)
- **Pesos**: 400 (Regular), 500 (Medium), 600 (Semi-bold)

### **Componentes**
- **Material-UI 5**: Componentes modernos e acessíveis
- **Modo Compacto**: Interface densa e eficiente (padrão)
- **Responsividade**: Adaptação automática para mobile/desktop
- **Micro-animações**: Transições suaves e feedback visual

## 🚀 Deploy e Produção

### **Hospedagem Recomendada**
- **Netlify**: Deploy automático via Git
- **Vercel**: Otimizado para React
- **GitHub Pages**: Gratuito para projetos open source
- **AWS S3 + CloudFront**: Para alta disponibilidade

### **Configuração de Produção**
```bash
# Build otimizado
npm run build

# Variáveis de ambiente
REACT_APP_VERSION=2.0.0
REACT_APP_ENV=production
```

## 🤝 Contribuição

Contribuições são bem-vindas! Para contribuir:

1. **Fork** o projeto
2. **Crie** uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. **Commit** suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. **Push** para a branch (`git push origin feature/AmazingFeature`)
5. **Abra** um Pull Request

### **Padrões de Código**
- **TypeScript**: Tipagem forte obrigatória
- **ESLint**: Seguir regras configuradas
- **Prettier**: Formatação automática
- **Commits**: Conventional Commits

## 📄 Licença

Este projeto está licenciado sob a **Licença MIT** - veja o arquivo [LICENSE](LICENSE) para detalhes.

### **Resumo da Licença MIT**
- ✅ **Uso comercial** permitido
- ✅ **Modificação** permitida
- ✅ **Distribuição** permitida
- ✅ **Uso privado** permitido
- ❌ **Sem garantia** ou responsabilidade

## 📞 Suporte e Contato

### **Suporte Técnico**
- **Email**: <EMAIL>
- **GitHub Issues**: Para reportar bugs
- **Documentação**: README.md e checklist.md

### **Comunidade**
- **Discord**: [Servidor da Comunidade](#)
- **Telegram**: [Grupo de Usuários](#)
- **YouTube**: [Canal com Tutoriais](#)

---

<div align="center">
  <img src="public/KVM-personal-trainer.png" alt="KHATFIT" width="100"/>

  <h3>🏋️‍♂️ KHATFIT</h3>
  <p><strong>Sistema Completo para Personal Trainers</strong></p>

  <p>
    <strong>🎯 100% Completo</strong> •
    <strong>🔐 Seguro</strong> •
    <strong>📊 Científico</strong> •
    <strong>🎨 Profissional</strong>
  </p>

  <p>Desenvolvido com ❤️ para profissionais de Educação Física</p>

  <p>
    <a href="http://localhost:3000">🚀 Testar Sistema</a> •
    <a href="mailto:<EMAIL>">📧 Contato</a> •
    <a href="#-licença">📄 Licença MIT</a>
  </p>
</div>
