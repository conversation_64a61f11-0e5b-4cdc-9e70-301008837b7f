# 📚 Documentação - KHATFIT

Esta pasta contém toda a documentação técnica e guias de instalação do projeto.

## 📋 Índice de Documentos

### ⚡ Para Quem Tem Pressa
- **[quick-start.md](./quick-start.md)** - Re<PERSON><PERSON> essencial (TL;DR)
  - Comandos básicos
  - Erros mais comuns
  - Verificação rápida

### 🚀 Instalação e Configuração
- **[instalacao.md](./instalacao.md)** - Guia completo de instalação
  - Problema conhecido com NPM
  - Solução usando YARN
  - Passo a passo detalhado
  - Scripts disponíveis

### 🔧 Troubleshooting
- **[troubleshooting-avancado.md](./troubleshooting-avancado.md)** - Soluções para problemas específicos
  - Problemas por sistema operacional
  - Erros comuns e soluções
  - Diagnóstico avançado
  - Otimizações de performance

## 🚨 IMPORTANTE: Use YARN, não NPM

Este projeto **NÃO FUNCIONA** com `npm install` devido a conflitos de dependências complexos entre:
- React
- TypeScript
- Dependências de build (webpack, babel, etc.)

### ✅ Comando Correto
```bash
yarn install
yarn dev
```

### ❌ Comando que NÃO Funciona
```bash
npm install  # ❌ VAI DAR ERRO!
```

## 🎯 Quick Start

```bash
# 1. Clonar repositório
git clone https://github.com/seu-usuario/khatfit-app.git
cd khatfit-app

# 2. Instalar dependências (USAR YARN!)
yarn install

# 3. Executar aplicação
yarn dev
```

## 📞 Suporte

Se encontrar problemas:

1. **Primeiro**: Leia [instalacao.md](./instalacao.md)
2. **Se persistir**: Consulte [troubleshooting-avancado.md](./troubleshooting-avancado.md)
3. **Ainda com problemas**: Verifique se está usando YARN (não NPM)
4. **Último recurso**: Documente o erro específico e sistema operacional

## 🔍 Estrutura da Documentação

```
.docs/
├── README.md                    # Este arquivo (índice)
├── quick-start.md              # Resumo essencial (TL;DR)
├── instalacao.md               # Guia de instalação principal
└── troubleshooting-avancado.md # Soluções para problemas específicos
```

## 📊 Status dos Documentos

| Documento | Status | Última Atualização |
|-----------|--------|-------------------|
| quick-start.md | ✅ Completo | 2024-12-19 |
| instalacao.md | ✅ Completo | 2024-12-19 |
| troubleshooting-avancado.md | ✅ Completo | 2024-12-19 |
| README.md | ✅ Completo | 2024-12-19 |

---

**Nota**: Esta documentação foi criada especificamente para resolver o problema de instalação com NPM. Sempre use YARN como gerenciador de pacotes para este projeto.
