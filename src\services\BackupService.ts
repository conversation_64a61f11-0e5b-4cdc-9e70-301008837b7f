import db from '../db/db';
import { Cliente } from '../models/Cliente';
import { AvaliacaoFisica } from '../models/AvaliacaoFisica';
import { Treino, Exercicio, Serie } from '../models/Treino';

// ============================================================================
// BACKUP SERVICE - SISTEMA DE BACKUP E RESTORE DE DADOS
// ============================================================================

export interface BackupData {
  version: string;
  timestamp: string;
  data: {
    clientes: Cliente[];
    avaliacoes_fisicas: AvaliacaoFisica[];
    dobras_cutaneas: any[];
    medidas_antropometricas: any[];
    treinos: Treino[];
    exercicios: Exercicio[];
    series: Serie[];
  };
  metadata: {
    totalClientes: number;
    totalAvaliacoes: number;
    totalTreinos: number;
    totalExercicios: number;
    totalSeries: number;
  };
}

export interface BackupOptions {
  includeClientes?: boolean;
  includeAvaliacoes?: boolean;
  includeTreinos?: boolean;
  compressData?: boolean;
}

export class BackupService {
  private static readonly BACKUP_VERSION = '1.0.0';
  private static readonly BACKUP_PREFIX = 'personal_trainer_backup';

  // --------------------------------------------------------------------------
  // EXPORTAÇÃO DE BACKUP COMPLETO
  // --------------------------------------------------------------------------
  
  static async criarBackupCompleto(options: BackupOptions = {}): Promise<BackupData> {
    const {
      includeClientes = true,
      includeAvaliacoes = true,
      includeTreinos = true
    } = options;

    try {
      const backupData: BackupData = {
        version: this.BACKUP_VERSION,
        timestamp: new Date().toISOString(),
        data: {
          clientes: [],
          avaliacoes_fisicas: [],
          dobras_cutaneas: [],
          medidas_antropometricas: [],
          treinos: [],
          exercicios: [],
          series: []
        },
        metadata: {
          totalClientes: 0,
          totalAvaliacoes: 0,
          totalTreinos: 0,
          totalExercicios: 0,
          totalSeries: 0
        }
      };

      // Backup de clientes
      if (includeClientes) {
        const clientes = await this.exportarClientes();
        backupData.data.clientes = clientes;
        backupData.metadata.totalClientes = clientes.length;
      }

      // Backup de avaliações físicas
      if (includeAvaliacoes) {
        const avaliacoes = await this.exportarAvaliacoes();
        backupData.data.avaliacoes_fisicas = avaliacoes.avaliacoes;
        backupData.data.dobras_cutaneas = avaliacoes.dobras;
        backupData.data.medidas_antropometricas = avaliacoes.medidas;
        backupData.metadata.totalAvaliacoes = avaliacoes.avaliacoes.length;
      }

      // Backup de treinos
      if (includeTreinos) {
        const treinos = await this.exportarTreinos();
        backupData.data.treinos = treinos.treinos;
        backupData.data.exercicios = treinos.exercicios;
        backupData.data.series = treinos.series;
        backupData.metadata.totalTreinos = treinos.treinos.length;
        backupData.metadata.totalExercicios = treinos.exercicios.length;
        backupData.metadata.totalSeries = treinos.series.length;
      }

      return backupData;
    } catch (error) {
      console.error('Erro ao criar backup:', error);
      throw new Error('Falha ao criar backup dos dados');
    }
  }

  // --------------------------------------------------------------------------
  // DOWNLOAD DO BACKUP COMO ARQUIVO JSON
  // --------------------------------------------------------------------------
  
  static async downloadBackup(options: BackupOptions = {}): Promise<void> {
    try {
      const backupData = await this.criarBackupCompleto(options);
      const jsonString = JSON.stringify(backupData, null, 2);
      
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `${this.BACKUP_PREFIX}_${timestamp}.json`;
      
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao fazer download do backup:', error);
      throw new Error('Falha ao fazer download do backup');
    }
  }

  // --------------------------------------------------------------------------
  // RESTAURAÇÃO DE BACKUP
  // --------------------------------------------------------------------------
  
  static async restaurarBackup(backupData: BackupData, options: {
    limparDadosExistentes?: boolean;
    validarIntegridade?: boolean;
  } = {}): Promise<void> {
    const { limparDadosExistentes = false, validarIntegridade = true } = options;

    try {
      // Validar integridade do backup
      if (validarIntegridade) {
        this.validarBackup(backupData);
      }

      // Limpar dados existentes se solicitado
      if (limparDadosExistentes) {
        await this.limparTodosDados();
      }

      // Restaurar dados na ordem correta (respeitando foreign keys)
      await this.restaurarClientes(backupData.data.clientes);
      await this.restaurarAvaliacoes(
        backupData.data.avaliacoes_fisicas,
        backupData.data.dobras_cutaneas,
        backupData.data.medidas_antropometricas
      );
      await this.restaurarTreinos(
        backupData.data.treinos,
        backupData.data.exercicios,
        backupData.data.series
      );

    } catch (error) {
      console.error('Erro ao restaurar backup:', error);
      throw new Error('Falha ao restaurar backup dos dados');
    }
  }

  // --------------------------------------------------------------------------
  // UPLOAD E PROCESSAMENTO DE ARQUIVO DE BACKUP
  // --------------------------------------------------------------------------
  
  static async processarArquivoBackup(file: File): Promise<BackupData> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        try {
          const jsonString = event.target?.result as string;
          const backupData = JSON.parse(jsonString) as BackupData;
          
          // Validar estrutura básica
          if (!backupData.version || !backupData.data) {
            throw new Error('Arquivo de backup inválido');
          }
          
          resolve(backupData);
        } catch (error) {
          reject(new Error('Erro ao processar arquivo de backup'));
        }
      };
      
      reader.onerror = () => {
        reject(new Error('Erro ao ler arquivo de backup'));
      };
      
      reader.readAsText(file);
    });
  }

  // --------------------------------------------------------------------------
  // MÉTODOS AUXILIARES PRIVADOS
  // --------------------------------------------------------------------------

  private static async exportarClientes(): Promise<Cliente[]> {
    const stmt = db.prepare('SELECT * FROM clientes ORDER BY id');
    return stmt.all() as Cliente[];
  }

  private static async exportarAvaliacoes(): Promise<{
    avaliacoes: AvaliacaoFisica[];
    dobras: any[];
    medidas: any[];
  }> {
    const avaliacoes = db.prepare('SELECT * FROM avaliacoes_fisicas ORDER BY id').all();
    const dobras = db.prepare('SELECT * FROM dobras_cutaneas ORDER BY avaliacao_id').all();
    const medidas = db.prepare('SELECT * FROM medidas_antropometricas ORDER BY avaliacao_id').all();
    
    return {
      avaliacoes: avaliacoes as AvaliacaoFisica[],
      dobras,
      medidas
    };
  }

  private static async exportarTreinos(): Promise<{
    treinos: Treino[];
    exercicios: Exercicio[];
    series: Serie[];
  }> {
    const treinos = db.prepare('SELECT * FROM treinos ORDER BY id').all();
    const exercicios = db.prepare('SELECT * FROM exercicios ORDER BY id').all();
    const series = db.prepare('SELECT * FROM series ORDER BY id').all();
    
    return {
      treinos: treinos as Treino[],
      exercicios: exercicios as Exercicio[],
      series: series as Serie[]
    };
  }

  private static async restaurarClientes(clientes: Cliente[]): Promise<void> {
    const stmt = db.prepare(`
      INSERT OR REPLACE INTO clientes 
      (id, nome, email, telefone, data_nascimento, sexo, observacoes, data_cadastro)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const transaction = db.transaction((clientes: Cliente[]) => {
      for (const cliente of clientes) {
        stmt.run(
          cliente.id,
          cliente.nome,
          cliente.email,
          cliente.telefone,
          cliente.data_nascimento,
          cliente.sexo,
          cliente.observacoes,
          cliente.data_cadastro
        );
      }
    });

    transaction(clientes);
  }

  private static async restaurarAvaliacoes(
    avaliacoes: AvaliacaoFisica[],
    dobras: any[],
    medidas: any[]
  ): Promise<void> {
    // Restaurar avaliações físicas
    const stmtAvaliacoes = db.prepare(`
      INSERT OR REPLACE INTO avaliacoes_fisicas
      (id, cliente_id, data_avaliacao, peso, altura, idade)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    // Restaurar dobras cutâneas
    const stmtDobras = db.prepare(`
      INSERT OR REPLACE INTO dobras_cutaneas
      (avaliacao_id, peitoral, tricipital, bicipital, axilar_media, suprailiaca, abdominal, coxa, panturrilha, percentual_gordura)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    // Restaurar medidas antropométricas
    const stmtMedidas = db.prepare(`
      INSERT OR REPLACE INTO medidas_antropometricas 
      (avaliacao_id, braco_direito, braco_esquerdo, antebraco_direito, antebraco_esquerdo, peitoral, cintura, abdomen, quadril, coxa_direita, coxa_esquerda, panturrilha_direita, panturrilha_esquerda)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const transaction = db.transaction(() => {
      // Inserir avaliações
      for (const avaliacao of avaliacoes) {
        stmtAvaliacoes.run(
          avaliacao.id,
          avaliacao.cliente_id,
          avaliacao.data_avaliacao,
          avaliacao.peso,
          avaliacao.altura,
          avaliacao.idade
        );
      }

      // Inserir dobras cutâneas
      for (const dobra of dobras) {
        stmtDobras.run(
          dobra.avaliacao_id,
          dobra.peitoral,
          dobra.tricipital,
          dobra.bicipital,
          dobra.axilar_media,
          dobra.suprailiaca,
          dobra.abdominal,
          dobra.coxa,
          dobra.panturrilha,
          dobra.percentual_gordura
        );
      }

      // Inserir medidas antropométricas
      for (const medida of medidas) {
        stmtMedidas.run(
          medida.avaliacao_id,
          medida.braco_direito,
          medida.braco_esquerdo,
          medida.antebraco_direito,
          medida.antebraco_esquerdo,
          medida.peitoral,
          medida.cintura,
          medida.abdomen,
          medida.quadril,
          medida.coxa_direita,
          medida.coxa_esquerda,
          medida.panturrilha_direita,
          medida.panturrilha_esquerda
        );
      }
    });

    transaction();
  }

  private static async restaurarTreinos(
    treinos: Treino[],
    exercicios: Exercicio[],
    series: Serie[]
  ): Promise<void> {
    // Implementar restauração de treinos, exercícios e séries
    // Similar ao método de avaliações
  }

  private static validarBackup(backupData: BackupData): void {
    if (!backupData.version) {
      throw new Error('Versão do backup não encontrada');
    }

    if (!backupData.data) {
      throw new Error('Dados do backup não encontrados');
    }

    // Validações adicionais podem ser adicionadas aqui
  }

  private static async limparTodosDados(): Promise<void> {
    const transaction = db.transaction(() => {
      db.prepare('DELETE FROM series').run();
      db.prepare('DELETE FROM exercicios').run();
      db.prepare('DELETE FROM treinos').run();
      db.prepare('DELETE FROM medidas_antropometricas').run();
      db.prepare('DELETE FROM dobras_cutaneas').run();
      db.prepare('DELETE FROM avaliacoes_fisicas').run();
      db.prepare('DELETE FROM clientes').run();
    });

    transaction();
  }
}

export default BackupService;
