import React from 'react';
import { Box, Container, Paper, Card } from '@mui/material';
import { colors } from '../../styles/colors';

// ============================================================================
// PAGE CONTAINER - CONTAINER PADRONIZADO PARA PÁGINAS
// ============================================================================

interface PageContainerProps {
  children: React.ReactNode;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  disableGutters?: boolean;
}

export const PageContainer: React.FC<PageContainerProps> = ({
  children,
  maxWidth = 'xl',
  disableGutters = false
}) => {
  return (
    <Container 
      maxWidth={maxWidth} 
      disableGutters={disableGutters}
      sx={{ py: { xs: 1, sm: 2 } }}
    >
      {children}
    </Container>
  );
};

// ============================================================================
// CONTENT CARD - CARTÃO DE CONTEÚDO PADRONIZADO
// ============================================================================

interface ContentCardProps {
  children: React.ReactNode;
  elevation?: number;
  padding?: 'small' | 'medium' | 'large';
  noBorder?: boolean;
  hover?: boolean;
}

export const ContentCard: React.FC<ContentCardProps> = ({
  children,
  elevation = 0,
  padding = 'medium',
  noBorder = false,
  hover = false
}) => {
  const getPadding = () => {
    switch (padding) {
      case 'small': return { xs: 1.5, sm: 2 };
      case 'large': return { xs: 3, sm: 4 };
      default: return { xs: 2, sm: 3 };
    }
  };

  return (
    <Card
      elevation={elevation}
      sx={{
        borderRadius: 3,
        border: noBorder ? 'none' : `1px solid ${colors.sea}20`,
        bgcolor: colors.cloud,
        boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)',
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        ...(hover && {
          '&:hover': {
            borderColor: `${colors.sea}40`,
            boxShadow: `0px 8px 16px ${colors.sea}19`,
            transform: 'translateY(-2px)'
          }
        })
      }}
    >
      <Box sx={{ p: getPadding() }}>
        {children}
      </Box>
    </Card>
  );
};

// ============================================================================
// CONTENT PAPER - PAPEL DE CONTEÚDO PADRONIZADO
// ============================================================================

interface ContentPaperProps {
  children: React.ReactNode;
  elevation?: number;
  padding?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'outlined' | 'gradient';
}

export const ContentPaper: React.FC<ContentPaperProps> = ({
  children,
  elevation = 0,
  padding = 'medium',
  variant = 'default'
}) => {
  const getPadding = () => {
    switch (padding) {
      case 'small': return { xs: 1.5, sm: 2 };
      case 'large': return { xs: 3, sm: 4 };
      default: return { xs: 2, sm: 3 };
    }
  };

  const getBackgroundStyle = () => {
    switch (variant) {
      case 'outlined':
        return {
          bgcolor: colors.cloud,
          border: `1px solid ${colors.sea}20`
        };
      case 'gradient':
        return {
          background: `linear-gradient(135deg, ${colors.cloud} 0%, ${colors.sea}05 100%)`
        };
      default:
        return {
          bgcolor: colors.cloud
        };
    }
  };

  return (
    <Paper
      elevation={elevation}
      sx={{
        borderRadius: 3,
        boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)',
        overflow: 'hidden',
        p: getPadding(),
        ...getBackgroundStyle()
      }}
    >
      {children}
    </Paper>
  );
};

// ============================================================================
// SECTION DIVIDER - DIVISOR DE SEÇÃO
// ============================================================================

interface SectionDividerProps {
  spacing?: 'small' | 'medium' | 'large';
}

export const SectionDivider: React.FC<SectionDividerProps> = ({
  spacing = 'medium'
}) => {
  const getSpacing = () => {
    switch (spacing) {
      case 'small': return { xs: 1, sm: 1.5 };
      case 'large': return { xs: 3, sm: 4 };
      default: return { xs: 2, sm: 3 };
    }
  };

  return <Box sx={{ mb: getSpacing() }} />;
};

// ============================================================================
// GRID CONTAINER - CONTAINER DE GRID PADRONIZADO
// ============================================================================

interface GridContainerProps {
  children: React.ReactNode;
  spacing?: number | { xs?: number; sm?: number; md?: number };
}

export const GridContainer: React.FC<GridContainerProps> = ({
  children,
  spacing = { xs: 2, sm: 3 }
}) => {
  return (
    <Box sx={{ 
      display: 'grid',
      gap: spacing,
      gridTemplateColumns: {
        xs: '1fr',
        sm: 'repeat(auto-fit, minmax(300px, 1fr))'
      }
    }}>
      {children}
    </Box>
  );
};

export default PageContainer;
