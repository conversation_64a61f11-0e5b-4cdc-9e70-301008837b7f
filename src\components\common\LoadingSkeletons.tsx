import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Stack
} from '@mui/material';
import { colors } from '../../styles/colors';

// ============================================================================
// SKELETON COMPONENTS PROFISSIONAIS
// ============================================================================

interface SkeletonProps {
  rows?: number;
  animated?: boolean;
}

// ----------------------------------------------------------------------------
// TABLE SKELETON - Para listas de dados
// ----------------------------------------------------------------------------
export const TableSkeleton: React.FC<SkeletonProps> = ({
  rows = 5,
  animated = true
}) => {

  return (
    <TableContainer 
      component={Paper} 
      elevation={0}
      sx={{ 
        borderRadius: { xs: 3, md: 4 },
        border: `1px solid ${colors.professional[200]}`,
        overflow: 'hidden',
        bgcolor: colors.white
      }}
    >
      <Table>
        <TableHead>
          <TableRow sx={{ bgcolor: `${colors.professional[50]}` }}>
            {[1, 2, 3, 4, 5, 6].map((col) => (
              <TableCell key={col} sx={{ py: 2 }}>
                <Skeleton 
                  variant="text" 
                  width="80%" 
                  height={20}
                  animation={animated ? 'wave' : false}
                  sx={{ 
                    bgcolor: `${colors.professional[200]}40`,
                    '&::after': {
                      background: `linear-gradient(90deg, transparent, ${colors.professional[100]}, transparent)`
                    }
                  }}
                />
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {Array.from({ length: rows }).map((_, index) => (
            <TableRow 
              key={index}
              sx={{ 
                '&:nth-of-type(even)': { 
                  bgcolor: `${colors.professional[50]}50` 
                },
                '&:hover': {
                  bgcolor: `${colors.energy[50]}30`
                }
              }}
            >
              {[1, 2, 3, 4, 5, 6].map((col) => (
                <TableCell key={col} sx={{ py: 2.5 }}>
                  <Skeleton 
                    variant="text" 
                    width={col === 1 ? "90%" : col === 6 ? "60%" : "70%"}
                    height={18}
                    animation={animated ? 'wave' : false}
                    sx={{ 
                      bgcolor: `${colors.professional[200]}30`,
                      '&::after': {
                        background: `linear-gradient(90deg, transparent, ${colors.professional[100]}80, transparent)`
                      }
                    }}
                  />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

// ----------------------------------------------------------------------------
// CARD SKELETON - Para cards de informações
// ----------------------------------------------------------------------------
export const CardSkeleton: React.FC<SkeletonProps> = ({ 
  rows = 3, 
  animated = true 
}) => {
  return (
    <Card 
      elevation={0}
      sx={{ 
        borderRadius: { xs: 3, md: 4 },
        border: `1px solid ${colors.professional[200]}`,
        bgcolor: colors.white,
        overflow: 'hidden'
      }}
    >
      <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
        {/* Header skeleton */}
        <Box sx={{ mb: 3 }}>
          <Skeleton 
            variant="text" 
            width="40%" 
            height={32}
            animation={animated ? 'wave' : false}
            sx={{ 
              mb: 1,
              bgcolor: `${colors.energy[200]}40`,
              '&::after': {
                background: `linear-gradient(90deg, transparent, ${colors.energy[100]}, transparent)`
              }
            }}
          />
          <Skeleton 
            variant="text" 
            width="60%" 
            height={20}
            animation={animated ? 'wave' : false}
            sx={{ 
              bgcolor: `${colors.professional[200]}30`
            }}
          />
        </Box>

        {/* Content skeleton */}
        <Stack spacing={2}>
          {Array.from({ length: rows }).map((_, index) => (
            <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Skeleton 
                variant="circular" 
                width={40} 
                height={40}
                animation={animated ? 'wave' : false}
                sx={{ 
                  bgcolor: `${colors.health[200]}40`,
                  flexShrink: 0
                }}
              />
              <Box sx={{ flex: 1 }}>
                <Skeleton 
                  variant="text" 
                  width="80%" 
                  height={20}
                  animation={animated ? 'wave' : false}
                  sx={{ 
                    mb: 0.5,
                    bgcolor: `${colors.professional[200]}30`
                  }}
                />
                <Skeleton 
                  variant="text" 
                  width="60%" 
                  height={16}
                  animation={animated ? 'wave' : false}
                  sx={{ 
                    bgcolor: `${colors.professional[200]}20`
                  }}
                />
              </Box>
            </Box>
          ))}
        </Stack>
      </CardContent>
    </Card>
  );
};

// ----------------------------------------------------------------------------
// DASHBOARD SKELETON - Para cards de estatísticas
// ----------------------------------------------------------------------------
export const DashboardSkeleton: React.FC<{ cards?: number }> = ({ 
  cards = 4 
}) => {
  return (
    <Box sx={{ display: 'grid', gap: 3, gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))' }}>
      {Array.from({ length: cards }).map((_, index) => (
        <Card 
          key={index}
          elevation={0}
          sx={{ 
            p: 3,
            borderRadius: 4,
            border: `1px solid ${colors.professional[200]}`,
            bgcolor: colors.white,
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* Animated background */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: '-100%',
              width: '100%',
              height: '100%',
              background: `linear-gradient(90deg, transparent, ${colors.professional[50]}, transparent)`,
              animation: 'shimmer 2s infinite',
              '@keyframes shimmer': {
                '0%': { left: '-100%' },
                '100%': { left: '100%' }
              }
            }}
          />
          
          <Box sx={{ position: 'relative', zIndex: 1 }}>
            <Skeleton 
              variant="text" 
              width="60%" 
              height={24}
              sx={{ mb: 2, bgcolor: `${colors.professional[200]}30` }}
            />
            <Skeleton 
              variant="text" 
              width="40%" 
              height={48}
              sx={{ mb: 1, bgcolor: `${colors.energy[200]}40` }}
            />
            <Skeleton 
              variant="text" 
              width="80%" 
              height={16}
              sx={{ bgcolor: `${colors.professional[200]}20` }}
            />
          </Box>
        </Card>
      ))}
    </Box>
  );
};

// ----------------------------------------------------------------------------
// LIST SKELETON - Para listas simples
// ----------------------------------------------------------------------------
export const ListSkeleton: React.FC<SkeletonProps> = ({ 
  rows = 6, 
  animated = true 
}) => {
  return (
    <Stack spacing={2}>
      {Array.from({ length: rows }).map((_, index) => (
        <Box 
          key={index}
          sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 2,
            p: 2,
            borderRadius: 3,
            border: `1px solid ${colors.professional[200]}`,
            bgcolor: colors.white,
            '&:hover': {
              borderColor: colors.energy[300]
            }
          }}
        >
          <Skeleton 
            variant="circular" 
            width={48} 
            height={48}
            animation={animated ? 'wave' : false}
            sx={{ 
              bgcolor: `${colors.health[200]}40`,
              flexShrink: 0
            }}
          />
          <Box sx={{ flex: 1 }}>
            <Skeleton 
              variant="text" 
              width="70%" 
              height={20}
              animation={animated ? 'wave' : false}
              sx={{ 
                mb: 0.5,
                bgcolor: `${colors.professional[200]}30`
              }}
            />
            <Skeleton 
              variant="text" 
              width="50%" 
              height={16}
              animation={animated ? 'wave' : false}
              sx={{ 
                bgcolor: `${colors.professional[200]}20`
              }}
            />
          </Box>
          <Skeleton 
            variant="rectangular" 
            width={80} 
            height={32}
            animation={animated ? 'wave' : false}
            sx={{ 
              borderRadius: 2,
              bgcolor: `${colors.energy[200]}30`
            }}
          />
        </Box>
      ))}
    </Stack>
  );
};

// ----------------------------------------------------------------------------
// FORM SKELETON - Para formulários
// ----------------------------------------------------------------------------
export const FormSkeleton: React.FC<{ fields?: number }> = ({ 
  fields = 6 
}) => {
  return (
    <Card 
      elevation={0}
      sx={{ 
        p: { xs: 2, sm: 3 },
        borderRadius: { xs: 3, md: 4 },
        border: `1px solid ${colors.professional[200]}`,
        bgcolor: colors.white
      }}
    >
      <Stack spacing={3}>
        {/* Form title */}
        <Skeleton 
          variant="text" 
          width="50%" 
          height={32}
          sx={{ bgcolor: `${colors.energy[200]}40` }}
        />
        
        {/* Form fields */}
        {Array.from({ length: fields }).map((_, index) => (
          <Box key={index}>
            <Skeleton 
              variant="text" 
              width="30%" 
              height={20}
              sx={{ 
                mb: 1,
                bgcolor: `${colors.professional[200]}30`
              }}
            />
            <Skeleton 
              variant="rectangular" 
              width="100%" 
              height={56}
              sx={{ 
                borderRadius: 2,
                bgcolor: `${colors.professional[200]}20`
              }}
            />
          </Box>
        ))}
        
        {/* Action buttons */}
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
          <Skeleton 
            variant="rectangular" 
            width={100} 
            height={40}
            sx={{ 
              borderRadius: 2,
              bgcolor: `${colors.professional[200]}30`
            }}
          />
          <Skeleton 
            variant="rectangular" 
            width={120} 
            height={40}
            sx={{ 
              borderRadius: 2,
              bgcolor: `${colors.energy[200]}40`
            }}
          />
        </Box>
      </Stack>
    </Card>
  );
};
