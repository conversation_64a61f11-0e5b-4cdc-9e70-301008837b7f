import { useMemo } from 'react';
import { createTheme, Theme } from '@mui/material/styles';
import { useSettings } from '../contexts/SettingsContext';
import { colors, darkColors, getThemeColors } from '../styles/colors';

// ============================================================================
// THEME HOOK - GERENCIAMENTO DE TEMA MATERIAL-UI
// ============================================================================

export const useAppTheme = (): Theme => {
  const { settings, isDarkMode } = useSettings();
  
  const theme = useMemo(() => {
    const themeColors = getThemeColors(isDarkMode);
    
    return createTheme({
      palette: {
        mode: isDarkMode ? 'dark' : 'light',
        primary: {
          main: isDarkMode ? darkColors.primary : colors.primary,
          light: isDarkMode ? darkColors.energy[400] : colors.energy[400],
          dark: isDarkMode ? darkColors.energy[600] : colors.energy[600],
          contrastText: '#FFFFFF',
        },
        secondary: {
          main: isDarkMode ? darkColors.secondary : colors.secondary,
          light: isDarkMode ? darkColors.professional[300] : colors.professional[300],
          dark: isDarkMode ? darkColors.professional[700] : colors.professional[700],
          contrastText: '#FFFFFF',
        },
        success: {
          main: isDarkMode ? darkColors.success : colors.success,
          light: isDarkMode ? darkColors.health[400] : colors.health[400],
          dark: isDarkMode ? darkColors.health[600] : colors.health[600],
        },
        warning: {
          main: isDarkMode ? darkColors.warning : colors.warning,
        },
        error: {
          main: isDarkMode ? darkColors.error : colors.error,
        },
        info: {
          main: isDarkMode ? darkColors.info : colors.info,
        },
        background: {
          default: isDarkMode ? darkColors.background.primary : colors.white,
          paper: isDarkMode ? darkColors.background.paper : colors.white,
        },
        text: {
          primary: isDarkMode ? darkColors.text.primary : colors.text.primary,
          secondary: isDarkMode ? darkColors.text.secondary : colors.text.secondary,
          disabled: isDarkMode ? darkColors.text.disabled : colors.text.disabled,
        },
        divider: isDarkMode ? darkColors.border.primary : colors.professional[200],
        action: {
          hover: isDarkMode ? darkColors.surface.hover : colors.professional[50],
          selected: isDarkMode ? darkColors.surface.selected : colors.professional[100],
          disabled: isDarkMode ? darkColors.text.disabled : colors.text.disabled,
          disabledBackground: isDarkMode ? darkColors.surface.secondary : colors.professional[100],
        },
      },
      typography: {
        fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
        fontSize: settings.fontSize === 'small' ? 12 : settings.fontSize === 'large' ? 16 : 14,
        h1: {
          fontWeight: 700,
          fontSize: settings.fontSize === 'small' ? '2rem' : settings.fontSize === 'large' ? '2.5rem' : '2.25rem',
          lineHeight: 1.2,
          color: isDarkMode ? darkColors.text.primary : colors.text.primary,
        },
        h2: {
          fontWeight: 600,
          fontSize: settings.fontSize === 'small' ? '1.75rem' : settings.fontSize === 'large' ? '2.125rem' : '1.875rem',
          lineHeight: 1.3,
          color: isDarkMode ? darkColors.text.primary : colors.text.primary,
        },
        h3: {
          fontWeight: 600,
          fontSize: settings.fontSize === 'small' ? '1.5rem' : settings.fontSize === 'large' ? '1.75rem' : '1.625rem',
          lineHeight: 1.4,
          color: isDarkMode ? darkColors.text.primary : colors.text.primary,
        },
        h4: {
          fontWeight: 600,
          fontSize: settings.fontSize === 'small' ? '1.25rem' : settings.fontSize === 'large' ? '1.5rem' : '1.375rem',
          lineHeight: 1.4,
          color: isDarkMode ? darkColors.text.primary : colors.text.primary,
        },
        h5: {
          fontWeight: 600,
          fontSize: settings.fontSize === 'small' ? '1.125rem' : settings.fontSize === 'large' ? '1.25rem' : '1.1875rem',
          lineHeight: 1.5,
          color: isDarkMode ? darkColors.text.primary : colors.text.primary,
        },
        h6: {
          fontWeight: 600,
          fontSize: settings.fontSize === 'small' ? '1rem' : settings.fontSize === 'large' ? '1.125rem' : '1.0625rem',
          lineHeight: 1.5,
          color: isDarkMode ? darkColors.text.primary : colors.text.primary,
        },
        body1: {
          fontSize: settings.fontSize === 'small' ? '0.875rem' : settings.fontSize === 'large' ? '1.125rem' : '1rem',
          lineHeight: 1.6,
          color: isDarkMode ? darkColors.text.primary : colors.text.primary,
        },
        body2: {
          fontSize: settings.fontSize === 'small' ? '0.75rem' : settings.fontSize === 'large' ? '1rem' : '0.875rem',
          lineHeight: 1.6,
          color: isDarkMode ? darkColors.text.secondary : colors.text.secondary,
        },
        button: {
          fontWeight: 600,
          textTransform: 'none',
          fontSize: settings.fontSize === 'small' ? '0.875rem' : settings.fontSize === 'large' ? '1rem' : '0.9375rem',
        },
      },
      shape: {
        borderRadius: settings.compactMode ? 6 : 12,
      },
      spacing: settings.compactMode ? 6 : 8,
      components: {
        MuiCssBaseline: {
          styleOverrides: {
            body: {
              backgroundColor: isDarkMode ? darkColors.background.primary : colors.white,
              color: isDarkMode ? darkColors.text.primary : colors.text.primary,
              transition: 'background-color 0.3s ease, color 0.3s ease',
            },
            '*': {
              '&::-webkit-scrollbar': {
                width: '8px',
                height: '8px',
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: isDarkMode ? darkColors.background.secondary : colors.professional[100],
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: isDarkMode ? darkColors.professional[400] : colors.professional[300],
                borderRadius: '4px',
                '&:hover': {
                  backgroundColor: isDarkMode ? darkColors.professional[300] : colors.professional[400],
                },
              },
            },
          },
        },
        MuiButton: {
          styleOverrides: {
            root: {
              borderRadius: settings.compactMode ? 6 : 12,
              padding: settings.compactMode ? '6px 16px' : '8px 20px',
              fontWeight: 600,
              textTransform: 'none',
              boxShadow: 'none',
              transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
              '&:hover': {
                boxShadow: isDarkMode ? darkColors.shadows.md : colors.shadows.md,
                transform: 'translateY(-1px)',
              },
              '&:active': {
                transform: 'translateY(0px)',
              },
            },
            contained: {
              '&:hover': {
                boxShadow: isDarkMode ? darkColors.shadows.lg : colors.shadows.lg,
              },
            },
          },
        },
        MuiCard: {
          styleOverrides: {
            root: {
              borderRadius: settings.compactMode ? 8 : 16,
              boxShadow: isDarkMode ? darkColors.shadows.sm : colors.shadows.sm,
              border: `1px solid ${isDarkMode ? darkColors.border.primary : colors.professional[200]}`,
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              '&:hover': {
                boxShadow: isDarkMode ? darkColors.shadows.md : colors.shadows.md,
                transform: settings.showAnimations ? 'translateY(-2px)' : 'none',
              },
            },
          },
        },
        MuiCardContent: {
          styleOverrides: {
            root: {
              padding: settings.compactMode ? '12px' : '16px',
              '&:last-child': {
                paddingBottom: settings.compactMode ? '12px' : '16px',
              },
            },
          },
        },
        MuiPaper: {
          styleOverrides: {
            root: {
              backgroundColor: isDarkMode ? darkColors.background.paper : colors.white,
              backgroundImage: 'none',
            },
            elevation1: {
              boxShadow: isDarkMode ? darkColors.shadows.sm : colors.shadows.sm,
            },
            elevation2: {
              boxShadow: isDarkMode ? darkColors.shadows.md : colors.shadows.md,
            },
            elevation3: {
              boxShadow: isDarkMode ? darkColors.shadows.lg : colors.shadows.lg,
            },
          },
        },
        MuiTextField: {
          styleOverrides: {
            root: {
              '& .MuiOutlinedInput-root': {
                borderRadius: settings.compactMode ? 6 : 12,
                transition: 'all 0.2s ease-in-out',
                '& fieldset': {
                  borderColor: isDarkMode ? darkColors.border.primary : colors.professional[300],
                },
                '&:hover fieldset': {
                  borderColor: isDarkMode ? darkColors.border.secondary : colors.professional[400],
                },
                '&.Mui-focused fieldset': {
                  borderColor: isDarkMode ? darkColors.primary : colors.primary,
                  borderWidth: '2px',
                },
              },
            },
          },
        },
        MuiChip: {
          styleOverrides: {
            root: {
              borderRadius: settings.compactMode ? 6 : 12,
              fontWeight: 500,
            },
          },
        },
        MuiTab: {
          styleOverrides: {
            root: {
              textTransform: 'none',
              fontWeight: 600,
              fontSize: settings.fontSize === 'small' ? '0.875rem' : settings.fontSize === 'large' ? '1rem' : '0.9375rem',
            },
          },
        },
        MuiTableCell: {
          styleOverrides: {
            root: {
              borderBottom: `1px solid ${isDarkMode ? darkColors.border.primary : colors.professional[200]}`,
              padding: settings.compactMode ? '8px 16px' : '12px 16px',
            },
            head: {
              backgroundColor: isDarkMode ? darkColors.background.secondary : colors.professional[50],
              fontWeight: 600,
              color: isDarkMode ? darkColors.text.primary : colors.text.primary,
            },
          },
        },
        MuiDrawer: {
          styleOverrides: {
            paper: {
              backgroundColor: isDarkMode ? darkColors.background.secondary : colors.white,
              borderRight: `1px solid ${isDarkMode ? darkColors.border.primary : colors.professional[200]}`,
            },
          },
        },
        MuiAppBar: {
          styleOverrides: {
            root: {
              backgroundColor: isDarkMode ? darkColors.background.secondary : colors.white,
              color: isDarkMode ? darkColors.text.primary : colors.text.primary,
              boxShadow: isDarkMode ? darkColors.shadows.sm : colors.shadows.sm,
              borderBottom: `1px solid ${isDarkMode ? darkColors.border.primary : colors.professional[200]}`,
            },
          },
        },
        MuiDialog: {
          styleOverrides: {
            paper: {
              backgroundColor: isDarkMode ? darkColors.background.paper : colors.white,
              backgroundImage: 'none',
            },
          },
        },
        MuiMenu: {
          styleOverrides: {
            paper: {
              backgroundColor: isDarkMode ? darkColors.background.paper : colors.white,
              border: `1px solid ${isDarkMode ? darkColors.border.primary : colors.professional[200]}`,
            },
          },
        },
        MuiTooltip: {
          styleOverrides: {
            tooltip: {
              backgroundColor: isDarkMode ? darkColors.professional[700] : colors.professional[800],
              color: isDarkMode ? darkColors.text.primary : colors.white,
              fontSize: '0.75rem',
              borderRadius: 8,
            },
          },
        },
        MuiToolbar: {
          styleOverrides: {
            root: {
              minHeight: settings.compactMode ? '48px' : '56px',
              padding: settings.compactMode ? '0 12px' : '0 16px',
            },
          },
        },
        MuiListItem: {
          styleOverrides: {
            root: {
              paddingTop: settings.compactMode ? '6px' : '8px',
              paddingBottom: settings.compactMode ? '6px' : '8px',
            },
          },
        },
        MuiListItemIcon: {
          styleOverrides: {
            root: {
              minWidth: settings.compactMode ? '36px' : '40px',
            },
          },
        },
      },
    });
  }, [isDarkMode, settings]);

  return theme;
};

export default useAppTheme;
