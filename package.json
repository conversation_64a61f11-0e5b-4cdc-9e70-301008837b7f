{"name": "khatfit-app", "version": "1.0.0", "description": "KHATFIT - Aplicativo para personal trainers gerenciarem clientes, avaliações físicas e treinos", "homepage": ".", "scripts": {"start": "react-app-rewired start", "start:8080": "PORT=8080 react-app-rewired start", "start:server": "PORT=8080 react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject", "dev": "yarn start", "dev:8080": "yarn start:8080", "dev:server": "yarn start:server", "server": "yarn start:server", "server:dev": "yarn dev:server"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.19", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/bcryptjs": "^3.0.0", "@types/jest": "^27.5.2", "@types/jspdf": "^2.0.0", "@types/node": "^16.18.65", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "assert": "^2.1.0", "axios": "^0.21.1", "bcryptjs": "^3.0.2", "browserify-zlib": "^0.2.0", "chart.js": "^4.4.0", "jspdf": "^3.0.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "react-scripts": "5.0.1", "recharts": "^2.15.1", "stream-browserify": "^3.0.0", "typescript": "^4.9.5", "util": "^0.12.5", "web-vitals": "^2.1.4"}, "devDependencies": {"browserify-fs": "^1.0.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "css-loader": "^7.1.2", "customize-cra": "^1.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "react-app-rewired": "^2.2.1", "stream-http": "^3.2.0", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "url": "^0.11.4", "webpack-dev-server": "^5.2.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}