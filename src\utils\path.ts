// Browser-compatible path utilities
// This replaces the Node.js 'path' module for browser environments

export const pathUtils = {
  /**
   * Join path segments together
   */
  join: (...parts: string[]): string => {
    return parts
      .filter(part => part && part.length > 0)
      .join('/')
      .replace(/\/+/g, '/');
  },

  /**
   * Get the directory name of a path
   */
  dirname: (filePath: string): string => {
    const parts = filePath.split('/');
    return parts.slice(0, -1).join('/') || '/';
  },

  /**
   * Get the base name of a path
   */
  basename: (filePath: string, ext?: string): string => {
    const parts = filePath.split('/');
    let base = parts[parts.length - 1] || '';
    
    if (ext && base.endsWith(ext)) {
      base = base.slice(0, -ext.length);
    }
    
    return base;
  },

  /**
   * Get the extension of a path
   */
  extname: (filePath: string): string => {
    const base = pathUtils.basename(filePath);
    const dotIndex = base.lastIndexOf('.');
    return dotIndex > 0 ? base.slice(dotIndex) : '';
  },

  /**
   * Resolve a path (simplified version)
   */
  resolve: (...parts: string[]): string => {
    return pathUtils.join(...parts);
  },

  /**
   * Check if path is absolute
   */
  isAbsolute: (filePath: string): boolean => {
    return filePath.startsWith('/') || /^[a-zA-Z]:/.test(filePath);
  }
};

export default pathUtils;
