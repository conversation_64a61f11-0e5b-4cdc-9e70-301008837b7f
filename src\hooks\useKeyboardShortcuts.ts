import { useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppToast } from '../contexts/ToastContext';

// ============================================================================
// KEYBOARD SHORTCUTS HOOK - SISTEMA DE ATALHOS DE TECLADO
// ============================================================================

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  description: string;
  action: () => void;
  category: 'navigation' | 'actions' | 'search' | 'general';
  disabled?: boolean;
}

interface UseKeyboardShortcutsOptions {
  onToggleSearch?: () => void;
  onToggleSettings?: () => void;
  onToggleTheme?: () => void;
  onNewClient?: () => void;
  onNewAssessment?: () => void;
  onNewWorkout?: () => void;
  onShowHelp?: () => void;
}

export const useKeyboardShortcuts = (options: UseKeyboardShortcutsOptions = {}) => {
  const navigate = useNavigate();
  const toast = useAppToast();
  const activeShortcuts = useRef<KeyboardShortcut[]>([]);

  const {
    onToggleSearch,
    onToggleSettings,
    onToggleTheme,
    onNewClient,
    onNewAssessment,
    onNewWorkout,
    onShowHelp
  } = options;

  // Definir atalhos disponíveis
  const shortcuts: KeyboardShortcut[] = [
    // Navegação
    {
      key: '1',
      ctrlKey: true,
      description: 'Ir para Início',
      action: () => navigate('/'),
      category: 'navigation'
    },
    {
      key: '2',
      ctrlKey: true,
      description: 'Ir para Clientes',
      action: () => navigate('/clientes'),
      category: 'navigation'
    },
    {
      key: '3',
      ctrlKey: true,
      description: 'Ir para Avaliações',
      action: () => navigate('/avaliacoes'),
      category: 'navigation'
    },
    {
      key: '4',
      ctrlKey: true,
      description: 'Ir para Treinos',
      action: () => navigate('/treinos'),
      category: 'navigation'
    },

    // Busca e Interface
    {
      key: 'k',
      ctrlKey: true,
      description: 'Abrir Busca Global',
      action: () => {
        onToggleSearch?.();
        toast.showInfo('Busca Global ativada');
      },
      category: 'search'
    },
    {
      key: '/',
      description: 'Focar na Busca',
      action: () => {
        onToggleSearch?.();
      },
      category: 'search'
    },

    // Configurações e Tema
    {
      key: ',',
      ctrlKey: true,
      description: 'Abrir Configurações',
      action: () => {
        onToggleSettings?.();
      },
      category: 'general'
    },
    {
      key: 'd',
      ctrlKey: true,
      shiftKey: true,
      description: 'Alternar Tema Escuro/Claro',
      action: () => {
        onToggleTheme?.();
        toast.showInfo('Tema alternado');
      },
      category: 'general'
    },

    // Ações Rápidas
    {
      key: 'n',
      ctrlKey: true,
      description: 'Novo Cliente',
      action: () => {
        onNewClient?.();
      },
      category: 'actions',
      disabled: !onNewClient
    },
    {
      key: 'n',
      ctrlKey: true,
      shiftKey: true,
      description: 'Nova Avaliação',
      action: () => {
        onNewAssessment?.();
      },
      category: 'actions',
      disabled: !onNewAssessment
    },
    {
      key: 't',
      ctrlKey: true,
      description: 'Novo Treino',
      action: () => {
        onNewWorkout?.();
      },
      category: 'actions',
      disabled: !onNewWorkout
    },

    // Ajuda
    {
      key: '?',
      shiftKey: true,
      description: 'Mostrar Atalhos de Teclado',
      action: () => {
        onShowHelp?.();
      },
      category: 'general'
    },
    {
      key: 'F1',
      description: 'Ajuda',
      action: () => {
        onShowHelp?.();
      },
      category: 'general'
    },

    // Navegação por Tabs
    {
      key: 'Tab',
      ctrlKey: true,
      description: 'Próxima Aba',
      action: () => {
        // Implementar navegação por tabs se necessário
      },
      category: 'navigation'
    },

    // Escape para fechar modais
    {
      key: 'Escape',
      description: 'Fechar Modal/Dialog',
      action: () => {
        // Será tratado pelos componentes individuais
      },
      category: 'general'
    }
  ];

  // Filtrar atalhos ativos (não desabilitados)
  const getActiveShortcuts = useCallback(() => {
    return shortcuts.filter(shortcut => !shortcut.disabled);
  }, [shortcuts]);

  // Verificar se uma combinação de teclas corresponde a um atalho
  const matchesShortcut = useCallback((event: KeyboardEvent, shortcut: KeyboardShortcut): boolean => {
    const keyMatches = event.key.toLowerCase() === shortcut.key.toLowerCase();
    const ctrlMatches = !!event.ctrlKey === !!shortcut.ctrlKey;
    const altMatches = !!event.altKey === !!shortcut.altKey;
    const shiftMatches = !!event.shiftKey === !!shortcut.shiftKey;
    const metaMatches = !!event.metaKey === !!shortcut.metaKey;

    return keyMatches && ctrlMatches && altMatches && shiftMatches && metaMatches;
  }, []);

  // Handler principal para eventos de teclado
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Ignorar se estiver em um campo de input/textarea
    const target = event.target as HTMLElement;
    const isInputField = target.tagName === 'INPUT' || 
                        target.tagName === 'TEXTAREA' || 
                        target.contentEditable === 'true' ||
                        target.closest('[contenteditable="true"]');

    // Permitir alguns atalhos mesmo em campos de input
    const allowedInInput = ['Escape', 'F1'];
    const isGlobalShortcut = event.ctrlKey || event.metaKey || allowedInInput.includes(event.key);

    if (isInputField && !isGlobalShortcut) {
      return;
    }

    const activeShortcuts = getActiveShortcuts();
    
    for (const shortcut of activeShortcuts) {
      if (matchesShortcut(event, shortcut)) {
        event.preventDefault();
        event.stopPropagation();
        
        try {
          shortcut.action();
        } catch (error) {
          console.error('Erro ao executar atalho:', error);
          toast.showError('Erro ao executar atalho de teclado');
        }
        
        break;
      }
    }
  }, [getActiveShortcuts, matchesShortcut, toast]);

  // Registrar/desregistrar event listeners
  useEffect(() => {
    activeShortcuts.current = getActiveShortcuts();
    
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, getActiveShortcuts]);

  // Função para obter atalhos por categoria
  const getShortcutsByCategory = useCallback((category: KeyboardShortcut['category']) => {
    return getActiveShortcuts().filter(shortcut => shortcut.category === category);
  }, [getActiveShortcuts]);

  // Função para formatar atalho para exibição
  const formatShortcut = useCallback((shortcut: KeyboardShortcut): string => {
    const parts: string[] = [];
    
    if (shortcut.ctrlKey) parts.push('Ctrl');
    if (shortcut.metaKey) parts.push('Cmd');
    if (shortcut.altKey) parts.push('Alt');
    if (shortcut.shiftKey) parts.push('Shift');
    
    parts.push(shortcut.key.toUpperCase());
    
    return parts.join(' + ');
  }, []);

  // Função para verificar se um atalho está disponível
  const isShortcutAvailable = useCallback((key: string, modifiers: Partial<Pick<KeyboardShortcut, 'ctrlKey' | 'altKey' | 'shiftKey' | 'metaKey'>> = {}): boolean => {
    const activeShortcuts = getActiveShortcuts();
    
    return !activeShortcuts.some(shortcut => 
      shortcut.key.toLowerCase() === key.toLowerCase() &&
      !!shortcut.ctrlKey === !!modifiers.ctrlKey &&
      !!shortcut.altKey === !!modifiers.altKey &&
      !!shortcut.shiftKey === !!modifiers.shiftKey &&
      !!shortcut.metaKey === !!modifiers.metaKey
    );
  }, [getActiveShortcuts]);

  return {
    shortcuts: getActiveShortcuts(),
    getShortcutsByCategory,
    formatShortcut,
    isShortcutAvailable
  };
};

// Hook para componentes que precisam de atalhos específicos
export const useComponentShortcuts = (shortcuts: KeyboardShortcut[]) => {
  const toast = useAppToast();

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    for (const shortcut of shortcuts) {
      if (shortcut.disabled) continue;

      const keyMatches = event.key.toLowerCase() === shortcut.key.toLowerCase();
      const ctrlMatches = !!event.ctrlKey === !!shortcut.ctrlKey;
      const altMatches = !!event.altKey === !!shortcut.altKey;
      const shiftMatches = !!event.shiftKey === !!shortcut.shiftKey;
      const metaMatches = !!event.metaKey === !!shortcut.metaKey;

      if (keyMatches && ctrlMatches && altMatches && shiftMatches && metaMatches) {
        event.preventDefault();
        event.stopPropagation();
        
        try {
          shortcut.action();
        } catch (error) {
          console.error('Erro ao executar atalho do componente:', error);
          toast.showError('Erro ao executar atalho');
        }
        
        break;
      }
    }
  }, [shortcuts, toast]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
};

export default useKeyboardShortcuts;
