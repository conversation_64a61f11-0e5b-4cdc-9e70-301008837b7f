import React from 'react';
import {
  <PERSON>,
  Typography,
  But<PERSON>,
  <PERSON>ack,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  PersonAdd as PersonAddIcon,
  Assessment as AssessmentIcon,
  FitnessCenter as FitnessCenterIcon,
  TrendingUp as TrendingUpIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { colors } from '../../styles/colors';

// ============================================================================
// EMPTY STATE COMPONENT - ESTADOS VAZIOS PROFISSIONAIS
// ============================================================================

interface EmptyStateProps {
  variant: 'clientes' | 'avaliacoes' | 'treinos' | 'dashboard' | 'search' | 'generic';
  title?: string;
  description?: string;
  actionLabel?: string;
  onAction?: () => void;
  showRefresh?: boolean;
  onRefresh?: () => void;
  size?: 'small' | 'medium' | 'large';
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  variant,
  title,
  description,
  actionLabel,
  onAction,
  showRefresh = false,
  onRefresh,
  size = 'medium'
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Configurações por variante
  const getConfig = () => {
    switch (variant) {
      case 'clientes':
        return {
          icon: <PersonAddIcon />,
          title: title || 'Nenhum cliente cadastrado',
          description: description || 'Comece adicionando seu primeiro cliente para começar a acompanhar sua evolução.',
          actionLabel: actionLabel || 'Adicionar Cliente',
          color: colors.energy[500],
          bgColor: colors.energy[50],
          illustration: 'clientes'
        };
      
      case 'avaliacoes':
        return {
          icon: <AssessmentIcon />,
          title: title || 'Nenhuma avaliação física',
          description: description || 'Realize a primeira avaliação física para acompanhar a composição corporal.',
          actionLabel: actionLabel || 'Nova Avaliação',
          color: colors.health[500],
          bgColor: colors.health[50],
          illustration: 'avaliacoes'
        };
      
      case 'treinos':
        return {
          icon: <FitnessCenterIcon />,
          title: title || 'Nenhum treino criado',
          description: description || 'Crie o primeiro treino personalizado para este cliente.',
          actionLabel: actionLabel || 'Criar Treino',
          color: colors.professional[500],
          bgColor: colors.professional[50],
          illustration: 'treinos'
        };
      
      case 'dashboard':
        return {
          icon: <TrendingUpIcon />,
          title: title || 'Dados insuficientes',
          description: description || 'Adicione clientes e avaliações para visualizar estatísticas.',
          actionLabel: actionLabel || 'Começar',
          color: colors.energy[500],
          bgColor: colors.energy[50],
          illustration: 'dashboard'
        };
      
      case 'search':
        return {
          icon: <SearchIcon />,
          title: title || 'Nenhum resultado encontrado',
          description: description || 'Tente ajustar os filtros ou termos de busca.',
          actionLabel: actionLabel || 'Limpar Filtros',
          color: colors.professional[500],
          bgColor: colors.professional[50],
          illustration: 'search'
        };
      
      default:
        return {
          icon: <RefreshIcon />,
          title: title || 'Nenhum item encontrado',
          description: description || 'Não há itens para exibir no momento.',
          actionLabel: actionLabel || 'Atualizar',
          color: colors.professional[500],
          bgColor: colors.professional[50],
          illustration: 'generic'
        };
    }
  };

  const config = getConfig();
  
  // Tamanhos responsivos
  const getSizes = () => {
    const baseSize = isMobile ? 0.8 : 1;
    
    switch (size) {
      case 'small':
        return {
          illustration: 120 * baseSize,
          iconSize: 32 * baseSize,
          titleSize: isMobile ? '1.25rem' : '1.5rem',
          descriptionSize: isMobile ? '0.875rem' : '1rem',
          spacing: 2,
          padding: 3
        };
      case 'large':
        return {
          illustration: 200 * baseSize,
          iconSize: 48 * baseSize,
          titleSize: isMobile ? '1.75rem' : '2rem',
          descriptionSize: isMobile ? '1rem' : '1.125rem',
          spacing: 4,
          padding: 6
        };
      default: // medium
        return {
          illustration: 160 * baseSize,
          iconSize: 40 * baseSize,
          titleSize: isMobile ? '1.5rem' : '1.75rem',
          descriptionSize: isMobile ? '0.875rem' : '1rem',
          spacing: 3,
          padding: 4
        };
    }
  };

  const sizes = getSizes();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        py: sizes.padding,
        px: 2,
        minHeight: size === 'large' ? '60vh' : size === 'small' ? '200px' : '40vh'
      }}
    >
      {/* Ilustração SVG */}
      <Box
        sx={{
          width: sizes.illustration,
          height: sizes.illustration,
          mb: sizes.spacing,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '50%',
          bgcolor: config.bgColor,
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Círculo decorativo de fundo */}
        <Box
          sx={{
            position: 'absolute',
            width: '80%',
            height: '80%',
            borderRadius: '50%',
            bgcolor: `${config.color}15`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          {/* Ícone principal */}
          <Box
            sx={{
              fontSize: sizes.iconSize,
              color: config.color,
              '& svg': {
                fontSize: 'inherit'
              }
            }}
          >
            {config.icon}
          </Box>
        </Box>

        {/* Elementos decorativos */}
        <Box
          sx={{
            position: 'absolute',
            top: '20%',
            right: '20%',
            width: 8,
            height: 8,
            borderRadius: '50%',
            bgcolor: config.color,
            opacity: 0.3,
            animation: 'float 3s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px)' },
              '50%': { transform: 'translateY(-10px)' }
            }
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: '25%',
            left: '15%',
            width: 6,
            height: 6,
            borderRadius: '50%',
            bgcolor: config.color,
            opacity: 0.2,
            animation: 'float 3s ease-in-out infinite 1s',
          }}
        />
      </Box>

      {/* Conteúdo textual */}
      <Stack spacing={sizes.spacing / 2} alignItems="center" sx={{ maxWidth: 400 }}>
        <Typography
          variant="h5"
          component="h3"
          sx={{
            fontSize: sizes.titleSize,
            fontWeight: 600,
            color: colors.professional[700],
            mb: 1
          }}
        >
          {config.title}
        </Typography>

        <Typography
          variant="body1"
          sx={{
            fontSize: sizes.descriptionSize,
            color: colors.professional[500],
            lineHeight: 1.6,
            mb: 2
          }}
        >
          {config.description}
        </Typography>

        {/* Botões de ação */}
        <Stack 
          direction={isMobile ? 'column' : 'row'} 
          spacing={2} 
          sx={{ mt: 2, width: isMobile ? '100%' : 'auto' }}
        >
          {onAction && (
            <Button
              variant="contained"
              size={size === 'small' ? 'small' : 'medium'}
              onClick={onAction}
              startIcon={config.icon}
              sx={{
                bgcolor: config.color,
                color: colors.white,
                px: 3,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontWeight: 600,
                boxShadow: `0 4px 12px ${config.color}30`,
                '&:hover': {
                  bgcolor: config.color,
                  filter: 'brightness(0.9)',
                  transform: 'translateY(-1px)',
                  boxShadow: `0 6px 16px ${config.color}40`
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              {config.actionLabel}
            </Button>
          )}

          {showRefresh && onRefresh && (
            <Button
              variant="outlined"
              size={size === 'small' ? 'small' : 'medium'}
              onClick={onRefresh}
              startIcon={<RefreshIcon />}
              sx={{
                borderColor: colors.professional[300],
                color: colors.professional[600],
                px: 3,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontWeight: 500,
                '&:hover': {
                  borderColor: colors.professional[400],
                  bgcolor: colors.professional[50],
                  transform: 'translateY(-1px)'
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              Atualizar
            </Button>
          )}
        </Stack>
      </Stack>
    </Box>
  );
};

// ============================================================================
// VARIANTES ESPECÍFICAS - COMPONENTES PRÉ-CONFIGURADOS
// ============================================================================

export const EmptyClientes: React.FC<{
  onAddCliente?: () => void;
  size?: 'small' | 'medium' | 'large';
}> = ({ onAddCliente, size = 'medium' }) => (
  <EmptyState
    variant="clientes"
    onAction={onAddCliente}
    size={size}
  />
);

export const EmptyAvaliacoes: React.FC<{
  onAddAvaliacao?: () => void;
  clienteNome?: string;
  size?: 'small' | 'medium' | 'large';
}> = ({ onAddAvaliacao, clienteNome, size = 'medium' }) => (
  <EmptyState
    variant="avaliacoes"
    description={clienteNome 
      ? `${clienteNome} ainda não possui avaliações físicas. Realize a primeira avaliação para acompanhar sua evolução.`
      : undefined
    }
    onAction={onAddAvaliacao}
    size={size}
  />
);

export const EmptyTreinos: React.FC<{
  onAddTreino?: () => void;
  clienteNome?: string;
  size?: 'small' | 'medium' | 'large';
}> = ({ onAddTreino, clienteNome, size = 'medium' }) => (
  <EmptyState
    variant="treinos"
    description={clienteNome 
      ? `Crie o primeiro treino personalizado para ${clienteNome}.`
      : undefined
    }
    onAction={onAddTreino}
    size={size}
  />
);

export const EmptySearch: React.FC<{
  onClearFilters?: () => void;
  searchTerm?: string;
  size?: 'small' | 'medium' | 'large';
}> = ({ onClearFilters, searchTerm, size = 'medium' }) => (
  <EmptyState
    variant="search"
    description={searchTerm 
      ? `Nenhum resultado encontrado para "${searchTerm}". Tente ajustar os termos de busca.`
      : undefined
    }
    onAction={onClearFilters}
    size={size}
  />
);

export default EmptyState;
