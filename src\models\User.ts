// Função simples de hash para desenvolvimento (substituir por bcrypt em produção)
const simpleHash = (password: string): string => {
  return btoa(password + 'salt'); // Base64 com salt simples
};

const verifyPassword = (password: string, hash: string): boolean => {
  return simpleHash(password) === hash;
};

// Função para acessar IndexedDB diretamente
const openDB = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('HyperPersonalDB', 1);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;

      if (!db.objectStoreNames.contains('users')) {
        const usersStore = db.createObjectStore('users', { keyPath: 'id', autoIncrement: true });
        usersStore.createIndex('email', 'email', { unique: true });
        usersStore.createIndex('role', 'role', { unique: false });
      }
    };
  });
};

// ============================================================================
// USER MODEL - MODELO DE USUÁRIO
// ============================================================================

export interface User {
  id: number;
  name: string;
  email: string;
  password: string;
  avatar?: string;
  phone?: string;
  clinic_name?: string;
  clinic_address?: string;
  clinic_phone?: string;
  role: 'admin' | 'trainer' | 'user';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export class UserModel {
  // Inicializar banco e criar usuário admin se necessário
  static async inicializar(): Promise<void> {
    const db = await openDB();

    // Verificar se existe algum usuário
    const transaction = db.transaction(['users'], 'readonly');
    const store = transaction.objectStore('users');
    const count = await new Promise<number>((resolve, reject) => {
      const request = store.count();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });

    // Se não há usuários, criar admin padrão
    if (count === 0) {
      await this.criarAdminPadrao();
    }
  }

  // Criar usuário administrador padrão
  static async criarAdminPadrao(): Promise<void> {
    const adminData = {
      name: 'Administrador',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin' as const,
      clinic_name: 'KHATFIT',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    await this.criar(adminData);
    console.log('Usuário administrador criado: <EMAIL> / admin123');
  }

  // Criar usuário
  static async criar(userData: Omit<User, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const db = await openDB();
    const hashedPassword = simpleHash(userData.password);

    const userToCreate = {
      name: userData.name,
      email: userData.email,
      password: hashedPassword,
      avatar: userData.avatar || null,
      phone: userData.phone || null,
      clinic_name: userData.clinic_name || null,
      clinic_address: userData.clinic_address || null,
      clinic_phone: userData.clinic_phone || null,
      role: userData.role || 'trainer',
      is_active: userData.is_active !== false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['users'], 'readwrite');
      const store = transaction.objectStore('users');
      const request = store.add(userToCreate);

      request.onsuccess = () => resolve(request.result as number);
      request.onerror = () => reject(request.error);
    });
  }

  // Autenticar usuário
  static async autenticar(email: string, password: string): Promise<User | null> {
    const db = await openDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['users'], 'readonly');
      const store = transaction.objectStore('users');
      const index = store.index('email');
      const request = index.get(email);

      request.onsuccess = () => {
        const user = request.result as User | undefined;

        if (!user || !user.is_active) {
          resolve(null);
          return;
        }

        const isValidPassword = verifyPassword(password, user.password);
        if (!isValidPassword) {
          resolve(null);
          return;
        }

        // Atualizar último acesso
        this.atualizarUltimoAcesso(user.id).then(() => {
          resolve(user);
        }).catch(() => {
          resolve(user); // Retorna o usuário mesmo se falhar ao atualizar último acesso
        });
      };

      request.onerror = () => reject(request.error);
    });
  }

  // Obter usuário por ID
  static async obterPorId(id: number): Promise<User | null> {
    const db = await openDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['users'], 'readonly');
      const store = transaction.objectStore('users');
      const request = store.get(id);

      request.onsuccess = () => {
        const user = request.result as User | undefined;
        resolve(user && user.is_active ? user : null);
      };

      request.onerror = () => reject(request.error);
    });
  }

  // Obter usuário por email
  static async obterPorEmail(email: string): Promise<User | null> {
    const db = await openDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['users'], 'readonly');
      const store = transaction.objectStore('users');
      const index = store.index('email');
      const request = index.get(email);

      request.onsuccess = () => {
        const user = request.result as User | undefined;
        resolve(user || null);
      };

      request.onerror = () => reject(request.error);
    });
  }

  // Obter todos os usuários
  static async obterTodos(): Promise<User[]> {
    const db = await openDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['users'], 'readonly');
      const store = transaction.objectStore('users');
      const request = store.getAll();

      request.onsuccess = () => {
        const users = request.result as User[];
        const activeUsers = users
          .filter(user => user.is_active)
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        resolve(activeUsers);
      };

      request.onerror = () => reject(request.error);
    });
  }

  // Atualizar usuário
  static async atualizar(id: number, userData: Partial<User>): Promise<boolean> {
    const db = await openDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['users'], 'readwrite');
      const store = transaction.objectStore('users');
      const getRequest = store.get(id);

      getRequest.onsuccess = () => {
        const user = getRequest.result;
        if (!user) {
          resolve(false);
          return;
        }

        // Campos que podem ser atualizados
        const allowedFields = [
          'name', 'email', 'avatar', 'phone',
          'clinic_name', 'clinic_address', 'clinic_phone', 'role', 'is_active'
        ];

        let hasChanges = false;
        for (const [key, value] of Object.entries(userData)) {
          if (allowedFields.includes(key) && value !== undefined) {
            user[key] = value;
            hasChanges = true;
          }
        }

        if (!hasChanges) {
          resolve(false);
          return;
        }

        user.updated_at = new Date().toISOString();

        const putRequest = store.put(user);
        putRequest.onsuccess = () => resolve(true);
        putRequest.onerror = () => reject(putRequest.error);
      };

      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  // Atualizar senha
  static async atualizarSenha(id: number, newPassword: string): Promise<boolean> {
    const db = await openDB();
    const hashedPassword = simpleHash(newPassword);

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['users'], 'readwrite');
      const store = transaction.objectStore('users');
      const getRequest = store.get(id);

      getRequest.onsuccess = () => {
        const user = getRequest.result;
        if (!user) {
          resolve(false);
          return;
        }

        user.password = hashedPassword;
        user.updated_at = new Date().toISOString();

        const putRequest = store.put(user);
        putRequest.onsuccess = () => resolve(true);
        putRequest.onerror = () => reject(putRequest.error);
      };

      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  // Desativar usuário (soft delete)
  static async desativar(id: number): Promise<boolean> {
    const db = await openDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['users'], 'readwrite');
      const store = transaction.objectStore('users');
      const getRequest = store.get(id);

      getRequest.onsuccess = () => {
        const user = getRequest.result;
        if (!user) {
          resolve(false);
          return;
        }

        user.is_active = false;
        user.updated_at = new Date().toISOString();

        const putRequest = store.put(user);
        putRequest.onsuccess = () => resolve(true);
        putRequest.onerror = () => reject(putRequest.error);
      };

      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  // Atualizar último acesso
  static async atualizarUltimoAcesso(id: number): Promise<void> {
    const db = await openDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['users'], 'readwrite');
      const store = transaction.objectStore('users');
      const getRequest = store.get(id);

      getRequest.onsuccess = () => {
        const user = getRequest.result;
        if (user) {
          user.updated_at = new Date().toISOString();
          const putRequest = store.put(user);
          putRequest.onsuccess = () => resolve();
          putRequest.onerror = () => reject(putRequest.error);
        } else {
          resolve();
        }
      };

      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  // Verificar se é o primeiro usuário (para setup inicial)
  static async isPrimeiroUsuario(): Promise<boolean> {
    const db = await openDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['users'], 'readonly');
      const store = transaction.objectStore('users');
      const request = store.count();

      request.onsuccess = () => resolve(request.result === 0);
      request.onerror = () => reject(request.error);
    });
  }
}
