# ⚡ Quick Start - KHATFIT

## 🚨 TL;DR - Só o Essencial

### Problema Principal
- **NPM NÃO FUNCIONA** ❌
- **USE YARN SEMPRE** ✅

### Comandos Essenciais
```bash
# Clonar
git clone https://github.com/seu-usuario/khatfit-app.git
cd khatfit-app

# Instalar (YARN OBRIGATÓRIO)
yarn install

# Executar
yarn dev
```

### Se Der Erro
1. **Verificar se tem Yarn**: `yarn --version`
2. **Instalar Yarn**: `npm install -g yarn`
3. **Limpar tudo**: `rm -rf node_modules/ && yarn install`
4. **Tentar novamente**: `yarn dev`

---

## 🔧 Instalação do Yarn

### Windows
```bash
npm install -g yarn
# OU
choco install yarn
```

### macOS
```bash
brew install yarn
# OU
npm install -g yarn
```

### Linux
```bash
# Ubuntu/Debian
curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | sudo apt-key add -
echo "deb https://dl.yarnpkg.com/debian/ stable main" | sudo tee /etc/apt/sources.list.d/yarn.list
sudo apt update && sudo apt install yarn

# OU simplesmente
npm install -g yarn
```

---

## 🚀 Scripts Disponíveis

```bash
yarn dev        # Desenvolvimento (React)
yarn start      # Servidor de desenvolvimento
yarn build      # Build de produção
yarn test       # Executar testes
```

---

## 🐛 Erros Mais Comuns

### "yarn: command not found"
```bash
npm install -g yarn
```

### "Port 3000 is already in use"
```bash
# Windows
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# Linux/Mac
lsof -ti:3000 | xargs kill -9
```

### "Module not found: better-sqlite3"
```bash
yarn remove better-sqlite3
yarn add better-sqlite3
```

### "Build failed"
```bash
yarn clean
yarn install
```

---

## ✅ Verificação de Sucesso

### Aplicação Funcionando
- ✅ Terminal mostra: "Compiled successfully!"
- ✅ Navegador abre: http://localhost:3000
- ✅ Interface carrega corretamente
- ✅ Consegue navegar entre páginas
- ✅ Consegue criar/editar clientes

### Logs de Sucesso
```
✓ Compiled successfully!
✓ Local: http://localhost:3000
✓ Database initialized successfully
```

---

## 📞 Ainda com Problemas?

1. **Leia**: [instalacao.md](./instalacao.md) - Guia completo
2. **Consulte**: [troubleshooting-avancado.md](./troubleshooting-avancado.md) - Soluções específicas
3. **Verifique**: Se está usando YARN (não NPM)
4. **Teste**: Apenas React primeiro (`yarn start`)

---

## 🎯 Resumo Final

- **SEMPRE use YARN** (nunca NPM)
- **Comandos**: `yarn install` → `yarn dev`
- **Se der erro**: Limpar cache e reinstalar
- **Funciona**: React + Electron + SQLite
- **Suporte**: Documentação completa na pasta `.docs/`
