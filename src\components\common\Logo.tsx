import React from 'react';
import { Box, Typography, SvgIcon } from '@mui/material';
import { colors } from '../../styles/colors';

// ============================================================================
// LOGO COMPONENTS - IDENTIDADE VISUAL PROFISSIONAL
// ============================================================================

interface LogoProps {
  variant?: 'horizontal' | 'vertical' | 'icon-only';
  size?: 'small' | 'medium' | 'large';
  color?: 'primary' | 'white' | 'dark';
  animated?: boolean;
}

// Ícone SVG customizado para fitness/treino
const FitnessIcon: React.FC<{ size?: number; color?: string }> = ({ 
  size = 32, 
  color = colors.professional[500] 
}) => (
  <SvgIcon 
    viewBox="0 0 100 100" 
    sx={{ 
      width: size, 
      height: size,
      color: color
    }}
  >
    {/* Haltere estilizado */}
    <g>
      {/* Peso esquerdo */}
      <rect x="5" y="35" width="15" height="30" rx="3" fill="currentColor" />
      <rect x="2" y="30" width="21" height="40" rx="5" fill="currentColor" opacity="0.8" />
      
      {/* Barra central */}
      <rect x="20" y="45" width="60" height="10" rx="5" fill="currentColor" />
      <circle cx="35" cy="50" r="3" fill="currentColor" opacity="0.6" />
      <circle cx="50" cy="50" r="3" fill="currentColor" opacity="0.6" />
      <circle cx="65" cy="50" r="3" fill="currentColor" opacity="0.6" />
      
      {/* Peso direito */}
      <rect x="80" y="35" width="15" height="30" rx="3" fill="currentColor" />
      <rect x="77" y="30" width="21" height="40" rx="5" fill="currentColor" opacity="0.8" />
      
      {/* Elementos decorativos */}
      <path 
        d="M25 25 Q50 15 75 25 Q75 35 50 30 Q25 35 25 25" 
        fill="currentColor" 
        opacity="0.3"
      />
      <path 
        d="M25 75 Q50 85 75 75 Q75 65 50 70 Q25 65 25 75" 
        fill="currentColor" 
        opacity="0.3"
      />
    </g>
  </SvgIcon>
);

// Componente principal do Logo
export const Logo: React.FC<LogoProps> = ({
  variant = 'horizontal',
  size = 'medium',
  color = 'primary',
  animated = false
}) => {
  const getSizes = () => {
    switch (size) {
      case 'small':
        return { icon: 24, fontSize: '1rem', spacing: 1 };
      case 'large':
        return { icon: 48, fontSize: '2rem', spacing: 2 };
      default:
        return { icon: 32, fontSize: '1.5rem', spacing: 1.5 };
    }
  };

  const getColors = () => {
    switch (color) {
      case 'white':
        return { primary: colors.white, secondary: colors.white };
      case 'dark':
        return { primary: colors.ocean, secondary: colors.professional[600] };
      default:
        return { primary: colors.professional[500], secondary: colors.ocean };
    }
  };

  const sizes = getSizes();
  const colorScheme = getColors();

  if (variant === 'icon-only') {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          ...(animated && {
            animation: 'pulse 2s ease-in-out infinite',
            '@keyframes pulse': {
              '0%, 100%': { transform: 'scale(1)' },
              '50%': { transform: 'scale(1.05)' }
            }
          })
        }}
      >
        <FitnessIcon size={sizes.icon} color={colorScheme.primary} />
      </Box>
    );
  }

  const logoContent = (
    <>
      <FitnessIcon size={sizes.icon} color={colorScheme.primary} />
      <Box sx={{ ml: variant === 'horizontal' ? sizes.spacing : 0 }}>
        <Typography
          variant="h6"
          component="span"
          sx={{
            fontWeight: 800,
            fontSize: sizes.fontSize,
            color: colorScheme.primary,
            fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
            letterSpacing: '-0.02em',
            lineHeight: 1
          }}
        >
          HYPER
        </Typography>
        <Typography
          variant="caption"
          component="div"
          sx={{
            fontSize: `calc(${sizes.fontSize} * 0.4)`,
            color: colorScheme.secondary,
            fontWeight: 500,
            letterSpacing: '0.1em',
            textTransform: 'uppercase',
            lineHeight: 1,
            mt: variant === 'vertical' ? 0.5 : 0
          }}
        >
          Personal Trainer
        </Typography>
      </Box>
    </>
  );

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: variant === 'horizontal' ? 'center' : 'flex-start',
        flexDirection: variant === 'vertical' ? 'column' : 'row',
        ...(animated && {
          animation: 'fadeInScale 0.8s ease-out',
          '@keyframes fadeInScale': {
            '0%': { 
              opacity: 0, 
              transform: 'scale(0.9) translateY(10px)' 
            },
            '100%': { 
              opacity: 1, 
              transform: 'scale(1) translateY(0)' 
            }
          }
        })
      }}
    >
      {logoContent}
    </Box>
  );
};

// ============================================================================
// LOGO ANIMADO PARA SPLASH SCREEN
// ============================================================================

export const AnimatedLogo: React.FC<{ onComplete?: () => void }> = ({ onComplete }) => {
  React.useEffect(() => {
    if (onComplete) {
      const timer = setTimeout(onComplete, 2000);
      return () => clearTimeout(timer);
    }
  }, [onComplete]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        animation: 'logoEntrance 2s ease-out',
        '@keyframes logoEntrance': {
          '0%': {
            opacity: 0,
            transform: 'scale(0.5) rotate(-10deg)'
          },
          '50%': {
            opacity: 0.8,
            transform: 'scale(1.1) rotate(5deg)'
          },
          '100%': {
            opacity: 1,
            transform: 'scale(1) rotate(0deg)'
          }
        }
      }}
    >
      <Box
        sx={{
          mb: 2,
          animation: 'iconSpin 2s ease-in-out',
          '@keyframes iconSpin': {
            '0%': { transform: 'rotate(0deg)' },
            '50%': { transform: 'rotate(180deg)' },
            '100%': { transform: 'rotate(360deg)' }
          }
        }}
      >
        <FitnessIcon size={64} color={colors.professional[500]} />
      </Box>
      
      <Typography
        variant="h3"
        sx={{
          fontWeight: 800,
          color: colors.professional[500],
          fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
          letterSpacing: '-0.02em',
          animation: 'textSlideUp 1s ease-out 0.5s both',
          '@keyframes textSlideUp': {
            '0%': {
              opacity: 0,
              transform: 'translateY(30px)'
            },
            '100%': {
              opacity: 1,
              transform: 'translateY(0)'
            }
          }
        }}
      >
        HYPER
      </Typography>
      
      <Typography
        variant="subtitle1"
        sx={{
          color: colors.ocean,
          fontWeight: 500,
          letterSpacing: '0.1em',
          textTransform: 'uppercase',
          animation: 'textFadeIn 1s ease-out 1s both',
          '@keyframes textFadeIn': {
            '0%': { opacity: 0 },
            '100%': { opacity: 1 }
          }
        }}
      >
        Personal Trainer System
      </Typography>
      
      {/* Barra de loading animada */}
      <Box
        sx={{
          width: 200,
          height: 3,
          bgcolor: `${colors.professional[500]}20`,
          borderRadius: 2,
          mt: 3,
          overflow: 'hidden',
          animation: 'barSlideIn 0.5s ease-out 1.2s both',
          '@keyframes barSlideIn': {
            '0%': {
              opacity: 0,
              transform: 'scaleX(0)'
            },
            '100%': {
              opacity: 1,
              transform: 'scaleX(1)'
            }
          }
        }}
      >
        <Box
          sx={{
            width: '100%',
            height: '100%',
            bgcolor: colors.professional[500],
            borderRadius: 2,
            animation: 'loadingProgress 1.5s ease-out 1.5s both',
            transformOrigin: 'left',
            '@keyframes loadingProgress': {
              '0%': { transform: 'scaleX(0)' },
              '100%': { transform: 'scaleX(1)' }
            }
          }}
        />
      </Box>
    </Box>
  );
};

// ============================================================================
// FAVICON COMPONENT
// ============================================================================

export const FaviconIcon: React.FC = () => (
  <SvgIcon viewBox="0 0 32 32">
    <rect width="32" height="32" rx="6" fill={colors.professional[500]} />
    <g transform="translate(6, 8)">
      <rect x="0" y="6" width="4" height="8" rx="1" fill="white" />
      <rect x="4" y="8" width="12" height="2" rx="1" fill="white" />
      <rect x="16" y="6" width="4" height="8" rx="1" fill="white" />
      <circle cx="7" cy="9" r="0.5" fill="white" opacity="0.7" />
      <circle cx="10" cy="9" r="0.5" fill="white" opacity="0.7" />
      <circle cx="13" cy="9" r="0.5" fill="white" opacity="0.7" />
    </g>
  </SvgIcon>
);

export default Logo;
