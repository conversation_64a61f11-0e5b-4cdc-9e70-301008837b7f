# Tutorial do KHATFIT - Sistema de Gerenciamento para Personal Trainers

## Índice
1. [Primeiros Passos](#primeiros-passos)
2. [Gerenciamento de Clientes](#gerenciamento-de-clientes)
3. [Avaliações Físicas](#avaliações-físicas)
4. [Planejamento de Treinos](#planejamento-de-treinos)
5. [Acompanhamento e Evolução](#acompanhamento-e-evolução)
6. [Dicas e Boas Práticas](#dicas-e-boas-práticas)

## Primeiros Passos

### Acessando o Sistema
1. Abra o aplicativo instalado em seu computador
2. A tela inicial apresentará um dashboard com resumo das suas atividades
3. No menu lateral, você encontrará todas as funcionalidades principais

### Interface Principal
- **Menu Lateral**: Navegação entre as diferentes seções
- **Área Principal**: Exibe o conteúdo da seção selecionada
- **Barra Superior**: Acesso rápido a funções importantes

## Gerenciamento de Clientes

### Cadastrando um Novo Cliente
1. Acesse "Clientes" no menu lateral
2. Clique no botão "Novo Cliente"
3. Preencha os dados básicos:
   - Nome completo
   - Data de nascimento
   - Sexo
   - Email (opcional)
   - Telefone (opcional)
   - Observações (opcional)
4. Clique em "Salvar"

### Gerenciando Clientes Existentes
- **Visualizar**: Clique no ícone de olho para ver detalhes
- **Editar**: Use o ícone de lápis para modificar informações
- **Excluir**: Utilize o ícone de lixeira (ação irreversível)
- **Buscar**: Use a barra de busca para encontrar clientes específicos

## Avaliações Físicas

### Realizando Nova Avaliação
1. Selecione o cliente desejado
2. Clique em "Nova Avaliação"
3. Preencha os dados da avaliação:

#### Dados Básicos
- Data da avaliação
- Peso
- Altura
- Idade
- Nível de atividade
- Intensidade dos exercícios
- Frequência semanal
- Tipo de trabalho
- Objetivo

#### Dobras Cutâneas
- Peitoral
- Tricipital
- Bicipital
- Axilar média
- Suprailiaca
- Abdominal
- Coxa
- Panturrilha

#### Medidas Antropométricas
- Braços (direito e esquerdo)
- Antebraços (direito e esquerdo)
- Peitoral
- Cintura
- Abdômen
- Quadril
- Coxas (direita e esquerda)
- Panturrilhas (direita e esquerda)

### Análises Automáticas
O sistema calculará automaticamente:
- IMC
- Percentual de gordura
- Composição corporal
- Taxa metabólica basal
- Gasto energético total

## Planejamento de Treinos

### Criando Novo Treino
1. Acesse a seção "Treinos"
2. Selecione o cliente
3. Clique em "Novo Treino"
4. Configure:
   - Tipo de treino (A, B, C, D, E, F)
   - Data de início
   - Observações

### Adicionando Exercícios
1. Selecione o treino desejado
2. Clique em "Adicionar Exercício"
3. Preencha:
   - Nome do exercício
   - Número de séries
   - Repetições
   - Carga
   - Observações específicas

### Gerenciando Treinos
- Organize treinos por tipo (A-F)
- Acompanhe volume de carga
- Ajuste progressões
- Faça anotações sobre execução

## Acompanhamento e Evolução

### Visualizando Progresso
1. Acesse "Evolução" no perfil do cliente
2. Analise:
   - Gráficos de evolução
   - Comparação entre avaliações
   - Histórico de medidas
   - Progressão de cargas

### Relatórios Disponíveis
- Composição corporal
- Evolução de medidas
- Histórico de treinos
- Progressão de cargas
- Assiduidade

## Dicas e Boas Práticas

### Para Avaliações
1. Sempre realize as medidas no mesmo horário
2. Use os mesmos pontos anatômicos para medições
3. Registre observações relevantes
4. Tire fotos de evolução (quando autorizado)

### Para Treinos
1. Mantenha progressão consistente
2. Registre adaptações necessárias
3. Anote feedback do cliente
4. Atualize cargas regularmente

### Gerenciamento Eficiente
1. Mantenha cadastros atualizados
2. Faça backup regularmente
3. Use as ferramentas de busca
4. Acompanhe indicadores de progresso

### Dicas de Uso do Sistema
1. Use atalhos de teclado para navegação rápida
2. Mantenha dados sempre atualizados
3. Utilize os filtros de busca
4. Explore os recursos de relatórios

## Suporte e Ajuda
- Para suporte técnico: <EMAIL>
- Consulte a documentação completa para mais detalhes
- Utilize o sistema de ajuda integrado para dúvidas específicas

---

**Observação**: Este tutorial cobre as funcionalidades principais do sistema. Para informações mais detalhadas sobre recursos específicos, consulte a documentação completa ou entre em contato com o suporte. 