#!/bin/bash

# Script para verificar portas disponíveis e configurar automaticamente
# Tenta usar 8080, se ocupada, tenta outras portas comuns

echo "🔍 Verificando portas disponíveis..."

# Lista de portas para tentar (em ordem de preferência)
PORTS=(8080 8081 8082 8083 8084 8085 3000 3001 3002 3003 5000 5001)
SELECTED_PORT=""

# Função para verificar se uma porta está disponível
check_port() {
    local port=$1
    if ! lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Porta disponível
    else
        return 1  # Porta ocupada
    fi
}

# Função para mostrar o que está usando uma porta
show_port_usage() {
    local port=$1
    echo "   Processo usando a porta $port:"
    lsof -Pi :$port -sTCP:LISTEN | head -2
}

# Verificar cada porta
for port in "${PORTS[@]}"; do
    echo -n "   Verificando porta $port... "
    if check_port $port; then
        echo "✅ Disponível"
        if [ -z "$SELECTED_PORT" ]; then
            SELECTED_PORT=$port
            echo "🎯 Porta selecionada: $SELECTED_PORT"
            break
        fi
    else
        echo "❌ Ocupada"
        show_port_usage $port
    fi
done

if [ -z "$SELECTED_PORT" ]; then
    echo ""
    echo "❌ Nenhuma porta disponível encontrada!"
    echo "   Tente matar alguns processos ou usar uma porta específica:"
    echo "   PORT=9999 yarn start"
    exit 1
fi

echo ""
echo "✅ Configuração recomendada:"
echo "   Porta: $SELECTED_PORT"
echo "   URL: http://localhost:$SELECTED_PORT"
echo ""

# Atualizar arquivo .env
if [ -f ".env" ]; then
    # Fazer backup do .env atual
    cp .env .env.backup
    
    # Atualizar a porta no .env
    sed -i "s/^PORT=.*/PORT=$SELECTED_PORT/" .env
    echo "📝 Arquivo .env atualizado com a porta $SELECTED_PORT"
else
    # Criar novo arquivo .env
    echo "PORT=$SELECTED_PORT" > .env
    echo "📝 Arquivo .env criado com a porta $SELECTED_PORT"
fi

echo ""
echo "🚀 Para iniciar o aplicativo, use:"
echo "   ./start-server.sh"
echo "   ou"
echo "   yarn start"
echo ""
echo "🌐 O aplicativo estará disponível em: http://localhost:$SELECTED_PORT"
