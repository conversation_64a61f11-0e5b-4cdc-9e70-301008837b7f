const { app, BrowserWindow } = require('electron');
const path = require('path');
const url = require('url');

// Configurar variável de ambiente para desenvolvimento
const PORT = process.env.PORT || '3000';
process.env.ELECTRON_START_URL = `http://localhost:${PORT}`;
console.log('🔧 Modo desenvolvimento - URL:', process.env.ELECTRON_START_URL);
console.log('🔧 Porta configurada:', PORT);

let mainWindow;

function createWindow() {
  console.log('🚀 Criando janela do Electron...');

  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    }
  });

  const startUrl = process.env.ELECTRON_START_URL || url.format({
    pathname: path.join(__dirname, 'build/index.html'),
    protocol: 'file:',
    slashes: true
  });

  console.log('🌐 Carregando URL:', startUrl);
  mainWindow.loadURL(startUrl);

  // Abrir o DevTools automaticamente (em desenvolvimento)
  if (process.env.ELECTRON_START_URL) {
    mainWindow.webContents.openDevTools();
  }

  // Adicionar logs de carregamento
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('✅ Página carregada com sucesso!');
  });

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.log('❌ Falha ao carregar página:', errorCode, errorDescription, validatedURL);
  });

  // Adicionar atalho F5 para reload
  mainWindow.webContents.on('before-input-event', (event, input) => {
    if (input.key === 'F5') {
      console.log('🔄 Recarregando página...');
      mainWindow.webContents.reload();
    }
  });

  mainWindow.on('closed', function () {
    mainWindow = null;
  });
}

app.on('ready', () => {
  console.log('⚡ Electron está pronto!');
  createWindow();
});

app.on('window-all-closed', function () {
  console.log('🔴 Todas as janelas foram fechadas');
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', function () {
  console.log('🔄 Aplicação foi ativada');
  if (mainWindow === null) {
    createWindow();
  }
});

