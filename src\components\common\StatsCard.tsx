import React from 'react';
import {
  Card,
  CardContent,
  Box,
  Typography,
  IconButton,
  Tooltip
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import { AnimatedCard, AnimatedCounter } from './AnimatedComponents';
import { colors } from '../../styles/colors';

// ============================================================================
// STATS CARD - CARTÃO DE ESTATÍSTICAS COMPACTO
// ============================================================================

interface StatsCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  description?: string;
  onClick?: () => void;
  delay?: number;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  color,
  description,
  onClick,
  delay = 0
}) => {
  return (
    <AnimatedCard
      hoverElevation={true}
      glowColor={color}
    >
      <Card
        onClick={onClick}
        sx={{
          height: '100%',
          cursor: onClick ? 'pointer' : 'default',
          borderRadius: 3,
          overflow: 'hidden',
          boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)',
          border: `1px solid ${color}20`,
          transition: 'all 0.3s ease',
          '&:hover': onClick ? {
            transform: 'translateY(-2px)',
            boxShadow: `0px 8px 24px ${color}30`,
            borderColor: `${color}40`
          } : {}
        }}
      >
        <CardContent sx={{ p: 2.5, textAlign: 'center' }}>
          {/* Ícone */}
          <Box 
            sx={{ 
              display: 'inline-flex',
              p: 1.5,
              borderRadius: '50%',
              mb: 1.5,
              bgcolor: `${color}15`,
              border: `2px solid ${color}30`
            }}
          >
            <Box sx={{ color: color, fontSize: '1.5rem' }}>
              {icon}
            </Box>
          </Box>

          {/* Título e Info */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
            <Typography 
              variant="h6" 
              component="div" 
              sx={{ 
                fontWeight: 600, 
                color: colors.professional[700],
                fontSize: '1rem'
              }}
            >
              {title}
            </Typography>
            {description && (
              <Tooltip title={description} arrow placement="top">
                <IconButton size="small" sx={{ ml: 0.5, p: 0.5 }}>
                  <InfoIcon fontSize="small" sx={{ color: colors.professional[400] }} />
                </IconButton>
              </Tooltip>
            )}
          </Box>

          {/* Valor */}
          <Typography
            variant="h3"
            sx={{
              mb: 0.5,
              color: color,
              fontWeight: 'bold',
              fontSize: '2.5rem',
              lineHeight: 1
            }}
          >
            <AnimatedCounter
              value={value}
              duration={1500 + delay}
            />
          </Typography>

          {/* Descrição resumida */}
          {description && (
            <Typography 
              variant="caption" 
              sx={{ 
                color: colors.professional[500],
                fontSize: '0.75rem',
                display: 'block',
                mt: 0.5
              }}
            >
              {description.length > 30 ? `${description.substring(0, 30)}...` : description}
            </Typography>
          )}
        </CardContent>
      </Card>
    </AnimatedCard>
  );
};

// ============================================================================
// MINI STATS CARD - VERSÃO AINDA MAIS COMPACTA
// ============================================================================

interface MiniStatsCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: string;
  subtitle?: string;
  onClick?: () => void;
}

export const MiniStatsCard: React.FC<MiniStatsCardProps> = ({
  title,
  value,
  icon,
  color,
  subtitle,
  onClick
}) => {
  return (
    <Card
      onClick={onClick}
      sx={{
        p: 2,
        cursor: onClick ? 'pointer' : 'default',
        borderRadius: 2,
        border: `1px solid ${color}20`,
        bgcolor: `${color}05`,
        transition: 'all 0.2s ease',
        '&:hover': onClick ? {
          bgcolor: `${color}10`,
          borderColor: `${color}40`,
          transform: 'translateY(-1px)'
        } : {}
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
        {/* Ícone */}
        <Box 
          sx={{ 
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 40,
            height: 40,
            borderRadius: '50%',
            bgcolor: `${color}20`,
            color: color,
            flexShrink: 0
          }}
        >
          {icon}
        </Box>

        {/* Conteúdo */}
        <Box sx={{ flexGrow: 1, minWidth: 0 }}>
          <Typography 
            variant="body2" 
            sx={{ 
              fontWeight: 500, 
              color: colors.professional[600],
              fontSize: '0.875rem',
              mb: 0.25
            }}
          >
            {title}
          </Typography>
          
          <Typography
            variant="h6"
            sx={{
              color: color,
              fontWeight: 'bold',
              fontSize: '1.25rem',
              lineHeight: 1
            }}
          >
            {typeof value === 'number' ? (
              <AnimatedCounter value={value} duration={1000} />
            ) : (
              value
            )}
          </Typography>

          {subtitle && (
            <Typography 
              variant="caption" 
              sx={{ 
                color: colors.professional[400],
                fontSize: '0.75rem'
              }}
            >
              {subtitle}
            </Typography>
          )}
        </Box>
      </Box>
    </Card>
  );
};

export default StatsCard;
