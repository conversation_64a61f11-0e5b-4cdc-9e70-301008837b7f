import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Tabs,
  Tab,
  Paper,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  IconButton,
  Tooltip
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import NotesIcon from '@mui/icons-material/Notes';
import BackupIcon from '@mui/icons-material/Backup';
import TuneIcon from '@mui/icons-material/Tune';
import ClienteList from '../components/Cliente/ClienteList';
import ClienteForm from '../components/Cliente/ClienteForm';
import AdvancedFilters, { FilterOption, ActiveFilter } from '../components/common/AdvancedFilters';
import BackupManager from '../components/common/BackupManager';
import { AnimatedButton, AnimatedFab } from '../components/common/AnimatedComponents';
import { Cliente } from '../models/Cliente';
import { useAppContext } from '../contexts/AppContext';
import { colors } from '../styles/colors';

const ClientesPage: React.FC = () => {
  const { clienteSelecionado, selecionarCliente } = useAppContext();
  const [tabAtiva, setTabAtiva] = useState(0);
  const [modoEdicao, setModoEdicao] = useState(false);
  const [dialogDetalhesAberto, setDialogDetalhesAberto] = useState(false);
  const [backupManagerAberto, setBackupManagerAberto] = useState(false);
  const [filtrosAvancados, setFiltrosAvancados] = useState(false);
  const [termoBusca, setTermoBusca] = useState('');
  const [filtrosAtivos, setFiltrosAtivos] = useState<ActiveFilter[]>([]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabAtiva(newValue);
  };

  const handleNovoCliente = () => {
    selecionarCliente(null);
    setModoEdicao(true);
    setTabAtiva(1);
  };

  const handleEditarCliente = (cliente: Cliente) => {
    selecionarCliente(cliente);
    setModoEdicao(true);
    setTabAtiva(1);
  };

  const handleVisualizarCliente = (cliente: Cliente) => {
    selecionarCliente(cliente);
    setDialogDetalhesAberto(true);
  };

  const handleSalvarCliente = () => {
    setModoEdicao(false);
    setTabAtiva(0);
  };

  const handleCancelarEdicao = () => {
    setModoEdicao(false);
    if (!clienteSelecionado) {
      setTabAtiva(0);
    }
  };

  const calcularIdade = (dataNascimento: string): number => {
    const hoje = new Date();
    const nascimento = new Date(dataNascimento);
    let idade = hoje.getFullYear() - nascimento.getFullYear();
    const m = hoje.getMonth() - nascimento.getMonth();
    
    if (m < 0 || (m === 0 && hoje.getDate() < nascimento.getDate())) {
      idade--;
    }
    
    return idade;
  };

  const formatarData = (data: string): string => {
    const partes = data.split('-');
    if (partes.length === 3) {
      return `${partes[2]}/${partes[1]}/${partes[0]}`;
    }
    return data;
  };

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(part => part[0])
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  // Configuração dos filtros avançados
  const filterOptions: FilterOption[] = [
    {
      id: 'sexo',
      label: 'Sexo',
      value: '',
      type: 'select',
      options: [
        { label: 'Masculino', value: 'M' },
        { label: 'Feminino', value: 'F' }
      ]
    },
    {
      id: 'idade_min',
      label: 'Idade Mínima',
      value: '',
      type: 'number'
    },
    {
      id: 'idade_max',
      label: 'Idade Máxima',
      value: '',
      type: 'number'
    },
    {
      id: 'data_cadastro',
      label: 'Data de Cadastro',
      value: '',
      type: 'date'
    }
  ];

  const handleBuscaChange = (valor: string) => {
    setTermoBusca(valor);
    // Implementar lógica de busca
  };

  const handleFiltrosChange = (filtros: ActiveFilter[]) => {
    setFiltrosAtivos(filtros);
    // Implementar lógica de filtros
  };

  const handleLimparFiltros = () => {
    setFiltrosAtivos([]);
    setTermoBusca('');
  };

  return (
    <Box>
      <Box sx={{
        mb: { xs: 2, sm: 3 },
        p: { xs: 2, sm: 3 },
        borderRadius: 3,
        background: `linear-gradient(45deg, ${colors.ocean} 30%, ${colors.sea} 90%)`,
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        alignItems: { xs: 'flex-start', sm: 'center' },
        gap: { xs: 2, sm: 0 },
        justifyContent: 'space-between'
      }}>
        <Typography variant="h4" fontWeight="bold" sx={{
          color: colors.cloud,
          fontSize: { xs: '1.5rem', sm: '2rem' }
        }}>
          Gerenciamento de Clientes
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <Tooltip title="Gerenciar Backup">
            <IconButton
              onClick={() => setBackupManagerAberto(true)}
              sx={{
                bgcolor: colors.professional[50],
                color: colors.professional[600],
                '&:hover': {
                  bgcolor: colors.professional[100],
                  color: colors.professional[700]
                }
              }}
            >
              <BackupIcon />
            </IconButton>
          </Tooltip>

          <AnimatedButton
            hoverScale={1.05}
            bounceOnHover={true}
            onClick={handleNovoCliente}
            sx={{
              bgcolor: colors.cloud,
              color: colors.ocean,
              borderRadius: 2,
              py: 1,
              px: 2,
              '&:hover': {
                bgcolor: colors.sea,
                color: colors.ocean,
                boxShadow: `0px 4px 8px ${colors.ocean}33`
              }
            }}
          >
            <AddIcon sx={{ mr: 1 }} />
            Novo Cliente
          </AnimatedButton>
        </Box>
      </Box>
      
      <Box sx={{ mb: { xs: 2, sm: 3 } }}>
        <Tabs 
          value={tabAtiva} 
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          sx={{
            '& .MuiTabs-indicator': {
              backgroundColor: colors.sea,
              height: 3,
              borderRadius: '3px 3px 0 0'
            },
            '& .MuiTab-root': {
              fontWeight: 500,
              fontSize: { xs: '0.875rem', sm: '1rem' },
              textTransform: 'none',
              color: `${colors.ocean}99`,
              '&.Mui-selected': {
                color: colors.sea,
                fontWeight: 600
              }
            }
          }}
        >
          <Tab label="Lista de Clientes" />
          <Tab label={modoEdicao ? (clienteSelecionado ? "Editar Cliente" : "Novo Cliente") : "Detalhes do Cliente"} />
        </Tabs>
      </Box>

      {tabAtiva === 0 && (
        <Box>
          {/* Filtros Avançados */}
          <AdvancedFilters
            searchPlaceholder="Buscar clientes por nome, email ou telefone..."
            searchValue={termoBusca}
            onSearchChange={handleBuscaChange}
            filterOptions={filterOptions}
            activeFilters={filtrosAtivos}
            onFiltersChange={handleFiltrosChange}
            onClearAll={handleLimparFiltros}
            showFilterCount={true}
          />

          <ClienteList
            onEditar={handleEditarCliente}
            onVisualizar={handleVisualizarCliente}
          />
        </Box>
      )}

      {tabAtiva === 1 && (
        <Box>
          {modoEdicao ? (
            <Paper
              elevation={0}
              sx={{
                p: { xs: 2, sm: 3 },
                borderRadius: 3,
                boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)',
                bgcolor: colors.cloud
              }}
            >
              <ClienteForm 
                clienteParaEditar={clienteSelecionado || undefined}
                onSalvar={handleSalvarCliente}
                onCancelar={handleCancelarEdicao}
              />
            </Paper>
          ) : clienteSelecionado ? (
            <Card
              elevation={0}
              sx={{
                borderRadius: 3,
                border: `1px solid ${colors.sea}`,
                boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)',
                overflow: 'hidden',
                bgcolor: colors.cloud,
                '&:hover': {
                  borderColor: colors.sea,
                  boxShadow: `0px 8px 16px ${colors.sea}19`
                }
              }}
            >
              <Box sx={{ 
                bgcolor: `${colors.sea}0A`, 
                p: { xs: 2, sm: 3 },
                borderBottom: `1px solid ${colors.sea}`,
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                alignItems: { xs: 'center', sm: 'flex-start' },
                gap: 2
              }}>
                <Avatar
                  sx={{
                    bgcolor: colors.sea,
                    width: 50,
                    height: 50,
                    boxShadow: `0px 4px 8px ${colors.sea}33`
                  }}
                >
                  {getInitials(clienteSelecionado.nome)}
                </Avatar>
                <Box sx={{ textAlign: { xs: 'center', sm: 'left' } }}>
                  <Typography variant="h5" fontWeight="bold" sx={{
                    color: colors.ocean,
                    fontSize: '1.25rem'
                  }}>
                    {clienteSelecionado.nome}
                  </Typography>
                  <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                    {clienteSelecionado.sexo === 'M' ? 'Masculino' : 'Feminino'}, {calcularIdade(clienteSelecionado.data_nascimento)} anos
                  </Typography>
                </Box>
              </Box>
              <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                <Grid container spacing={{ xs: 2, sm: 3 }}>
                  <Grid item xs={12} sm={6} md={4}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ 
                        bgcolor: `${colors.sea}1A`, 
                        p: 1, 
                        borderRadius: '50%',
                        display: 'flex',
                        mr: 2
                      }}>
                        <CalendarTodayIcon sx={{ color: colors.sea }} />
                      </Box>
                      <Box>
                        <Typography variant="body2" sx={{ color: `${colors.ocean}80` }}>
                          Data de Nascimento
                        </Typography>
                        <Typography fontWeight={500} sx={{ color: colors.ocean }}>
                          {formatarData(clienteSelecionado.data_nascimento)}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={4}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ 
                        bgcolor: `${colors.sea}1A`, 
                        p: 1, 
                        borderRadius: '50%',
                        display: 'flex',
                        mr: 2
                      }}>
                        <EmailIcon sx={{ color: colors.sea }} />
                      </Box>
                      <Box>
                        <Typography variant="body2" sx={{ color: `${colors.ocean}80` }}>
                          Email
                        </Typography>
                        <Typography fontWeight={500} sx={{ color: colors.ocean }}>
                          {clienteSelecionado.email}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6} md={4}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ 
                        bgcolor: `${colors.sea}1A`, 
                        p: 1, 
                        borderRadius: '50%',
                        display: 'flex',
                        mr: 2
                      }}>
                        <PhoneIcon sx={{ color: colors.sea }} />
                      </Box>
                      <Box>
                        <Typography variant="body2" sx={{ color: `${colors.ocean}80` }}>
                          Telefone
                        </Typography>
                        <Typography fontWeight={500} sx={{ color: colors.ocean }}>
                          {clienteSelecionado.telefone}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>

                {clienteSelecionado.observacoes && (
                  <Box sx={{ mt: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ 
                        bgcolor: `${colors.sea}1A`, 
                        p: 1, 
                        borderRadius: '50%',
                        display: 'flex',
                        mr: 2
                      }}>
                        <NotesIcon sx={{ color: colors.sea }} />
                      </Box>
                      <Typography variant="h6" sx={{ color: colors.ocean }}>
                        Observações
                      </Typography>
                    </Box>
                    <Typography sx={{ color: `${colors.ocean}99`, whiteSpace: 'pre-line' }}>
                      {clienteSelecionado.observacoes}
                    </Typography>
                  </Box>
                )}
              </CardContent>
              <Divider sx={{ borderColor: `${colors.ocean}1A` }} />
              <CardActions sx={{ p: 2, justifyContent: 'flex-end' }}>
                <Button 
                  variant="outlined"
                  onClick={() => setTabAtiva(0)}
                  sx={{
                    borderColor: colors.sea,
                    color: colors.sea,
                    mr: 1,
                    '&:hover': {
                      borderColor: colors.ocean,
                      color: colors.ocean,
                      bgcolor: `${colors.sea}0A`
                    }
                  }}
                >
                  Voltar
                </Button>
                <Button 
                  variant="contained"
                  onClick={() => handleEditarCliente(clienteSelecionado)}
                  sx={{
                    bgcolor: colors.sea,
                    '&:hover': {
                      bgcolor: colors.ocean,
                      boxShadow: `0px 4px 8px ${colors.sea}33`
                    }
                  }}
                >
                  Editar
                </Button>
              </CardActions>
            </Card>
          ) : null}
        </Box>
      )}

      {/* Diálogo de detalhes do cliente */}
      <Dialog
        open={dialogDetalhesAberto}
        onClose={() => setDialogDetalhesAberto(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            overflow: 'hidden',
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)'
          }
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: `${colors.sea}0A`, 
          p: 3,
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          <Avatar 
            sx={{ 
              bgcolor: colors.sea, 
              width: 40, 
              height: 40,
              boxShadow: `0px 4px 8px ${colors.sea}33`
            }}
          >
            {clienteSelecionado && getInitials(clienteSelecionado.nome)}
          </Avatar>
          <Typography variant="h6" fontWeight="bold" sx={{ color: colors.ocean }}>
            Detalhes do Cliente
          </Typography>
        </DialogTitle>
        <DialogContent dividers sx={{ p: 3, bgcolor: colors.cloud }}>
          {clienteSelecionado && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h5" fontWeight="bold" sx={{ color: colors.ocean }} gutterBottom>
                  {clienteSelecionado.nome}
                </Typography>
                <Typography variant="body2" sx={{ color: `${colors.ocean}99`, mb: 2 }}>
                  {clienteSelecionado.sexo === 'M' ? 'Masculino' : 'Feminino'}, {calcularIdade(clienteSelecionado.data_nascimento)} anos
                </Typography>
                <Divider sx={{ my: 2, borderColor: `${colors.ocean}1A` }} />
              </Grid>
              
              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ 
                    bgcolor: `${colors.sea}1A`, 
                    p: 1, 
                    borderRadius: '50%',
                    display: 'flex',
                    mr: 2
                  }}>
                    <CalendarTodayIcon sx={{ color: colors.sea }} />
                  </Box>
                  <Box>
                    <Typography variant="body2" sx={{ color: `${colors.ocean}80` }}>
                      Data de Nascimento
                    </Typography>
                    <Typography fontWeight={500} sx={{ color: colors.ocean }}>
                      {formatarData(clienteSelecionado.data_nascimento)}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              
              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ 
                    bgcolor: `${colors.sea}1A`, 
                    p: 1, 
                    borderRadius: '50%',
                    display: 'flex',
                    mr: 2
                  }}>
                    <CalendarTodayIcon sx={{ color: colors.sea }} />
                  </Box>
                  <Box>
                    <Typography variant="body2" sx={{ color: `${colors.ocean}80` }}>
                      Data de Cadastro
                    </Typography>
                    <Typography fontWeight={500} sx={{ color: colors.ocean }}>
                      {formatarData(clienteSelecionado.data_cadastro)}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              
              {clienteSelecionado.email && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ 
                      bgcolor: `${colors.sea}1A`, 
                      p: 1, 
                      borderRadius: '50%',
                      display: 'flex',
                      mr: 2
                    }}>
                      <EmailIcon sx={{ color: colors.sea }} />
                    </Box>
                    <Box>
                      <Typography variant="body2" sx={{ color: `${colors.ocean}80` }}>
                        Email
                      </Typography>
                      <Typography fontWeight={500} sx={{ color: colors.ocean }}>
                        {clienteSelecionado.email}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              )}
              
              {clienteSelecionado.telefone && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ 
                      bgcolor: `${colors.sea}1A`, 
                      p: 1, 
                      borderRadius: '50%',
                      display: 'flex',
                      mr: 2
                    }}>
                      <PhoneIcon sx={{ color: colors.sea }} />
                    </Box>
                    <Box>
                      <Typography variant="body2" sx={{ color: `${colors.ocean}80` }}>
                        Telefone
                      </Typography>
                      <Typography fontWeight={500} sx={{ color: colors.ocean }}>
                        {clienteSelecionado.telefone}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              )}
              
              {clienteSelecionado.observacoes && (
                <Grid item xs={12}>
                  <Box sx={{ mt: 2, p: 3, bgcolor: `${colors.sea}0A`, borderRadius: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                      <Box sx={{ 
                        bgcolor: `${colors.sea}1A`, 
                        p: 1, 
                        borderRadius: '50%',
                        display: 'flex',
                        mr: 2
                      }}>
                        <NotesIcon sx={{ color: colors.sea }} />
                      </Box>
                      <Typography variant="subtitle1" fontWeight={500} sx={{ color: colors.ocean }}>
                        Observações
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ pl: 6, color: `${colors.ocean}99` }}>
                      {clienteSelecionado.observacoes}
                    </Typography>
                  </Box>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2, bgcolor: colors.cloud }}>
          <Button 
            onClick={() => {
              setDialogDetalhesAberto(false);
              handleEditarCliente(clienteSelecionado!);
            }}
            sx={{ 
              color: colors.sea,
              fontWeight: 500,
              '&:hover': {
                bgcolor: `${colors.sea}0A`
              }
            }}
          >
            Editar
          </Button>
          <Button 
            onClick={() => setDialogDetalhesAberto(false)}
            sx={{ 
              color: `${colors.ocean}99`,
              fontWeight: 500,
              '&:hover': {
                bgcolor: `${colors.ocean}0A`,
                color: colors.ocean
              }
            }}
          >
            Fechar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Backup Manager */}
      <BackupManager
        open={backupManagerAberto}
        onClose={() => setBackupManagerAberto(false)}
      />

      {/* FAB para ações rápidas */}
      <Box sx={{ position: 'fixed', bottom: 24, right: 24, zIndex: 1000 }}>
        <AnimatedFab
          color={colors.energy[500]}
          size="large"
          onClick={handleNovoCliente}
          pulseOnHover={true}
          rippleEffect={true}
        >
          <AddIcon />
        </AnimatedFab>
      </Box>
    </Box>
  );
};

export default ClientesPage; 