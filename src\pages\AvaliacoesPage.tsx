import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,

  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Tooltip,
  Paper,
  Avatar,
  Stack
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import PersonIcon from '@mui/icons-material/Person';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import HeightIcon from '@mui/icons-material/Height';
import ScaleIcon from '@mui/icons-material/Scale';
import CalculateIcon from '@mui/icons-material/Calculate';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import MonitorWeightIcon from '@mui/icons-material/MonitorWeight';
import StraightenIcon from '@mui/icons-material/Straighten';
import AvaliacaoFisicaList from '../components/AvaliacaoFisica/AvaliacaoFisicaList';
import AvaliacaoFisicaForm from '../components/AvaliacaoFisica/AvaliacaoFisicaForm';
import { AvaliacaoFisica } from '../models/AvaliacaoFisica';
import { useAppContext } from '../contexts/AppContext';
import { useAvaliacaoFisica } from '../contexts/AvaliacaoFisicaContext';
import EvolucaoGraficos from '../components/AvaliacaoFisica/EvolucaoGraficos';
import ComparacaoAvaliacoes from '../components/AvaliacaoFisica/ComparacaoAvaliacoes';
import { colors } from '../styles/colors';

const AvaliacoesPage: React.FC = () => {
  const { clientes } = useAppContext();
  const {
    avaliacoes,
    avaliacaoSelecionada,
    carregando,
    selecionarAvaliacao,
    excluirAvaliacao,
    buscarAvaliacoes
  } = useAvaliacaoFisica();

  const [tabAtiva, setTabAtiva] = useState(0);
  const [modoEdicao, setModoEdicao] = useState(false);
  const [dialogClienteAberto, setDialogClienteAberto] = useState(false);
  const [clienteSelecionadoId, setClienteSelecionadoId] = useState<number | null>(null);
  const [dialogDetalhesAberto, setDialogDetalhesAberto] = useState(false);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabAtiva(newValue);
  };

  const handleNovaAvaliacao = () => {
    selecionarAvaliacao(null);
    setDialogClienteAberto(true);
  };

  const handleSelecionarCliente = (id: number | undefined) => {
    if (id) {
      setClienteSelecionadoId(id);
      setDialogClienteAberto(false);
      setModoEdicao(true);
      setTabAtiva(1);
    }
  };

  const handleEditarAvaliacao = (avaliacao: AvaliacaoFisica) => {
    selecionarAvaliacao(avaliacao);
    setClienteSelecionadoId(avaliacao.cliente_id);
    setModoEdicao(true);
    setTabAtiva(1);
  };

  const handleVisualizarAvaliacao = (avaliacao: AvaliacaoFisica) => {
    selecionarAvaliacao(avaliacao);
    setDialogDetalhesAberto(true);
  };

  const handleSalvarAvaliacao = async () => {
    setModoEdicao(false);
    setTabAtiva(0);
  };

  const handleCancelarEdicao = () => {
    setModoEdicao(false);
    if (!avaliacaoSelecionada) {
      setTabAtiva(0);
    }
  };

  const calcularIMC = (peso: number, altura: number): number => {
    const alturaMetros = altura / 100;
    return Number((peso / (alturaMetros * alturaMetros)).toFixed(2));
  };

  const classificarIMC = (imc: number): string => {
    if (imc < 18.5) return 'Abaixo do Peso';
    if (imc < 25) return 'Peso Normal';
    if (imc < 30) return 'Sobrepeso';
    if (imc < 35) return 'Obesidade Grau I';
    if (imc < 40) return 'Obesidade Grau II';
    return 'Obesidade Grau III';
  };

  const formatarData = (data: string): string => {
    const partes = data.split('-');
    if (partes.length === 3) {
      return `${partes[2]}/${partes[1]}/${partes[0]}`;
    }
    return data;
  };

  // Função para obter a cor do IMC baseada na classificação
  const getCorIMC = (imc: number): string => {
    if (imc < 18.5) return '#FFC107'; // Amarelo - Abaixo do Peso
    if (imc < 25) return '#4CAF50'; // Verde - Peso Normal
    if (imc < 30) return '#FF9800'; // Laranja - Sobrepeso
    if (imc < 35) return '#F44336'; // Vermelho claro - Obesidade Grau I
    if (imc < 40) return '#E91E63'; // Rosa - Obesidade Grau II
    return '#9C27B0'; // Roxo - Obesidade Grau III
  };

  const calcularMassaMuscular = (peso: number, percentualGordura: number): number => {
    const massaGorda = peso * (percentualGordura / 100);
    return peso - massaGorda;
  };

  return (
    <Box>
      <Box sx={{
        mb: { xs: 2, sm: 3 },
        p: { xs: 2, sm: 3 },
        borderRadius: 3,
        background: `linear-gradient(45deg, ${colors.ocean} 30%, ${colors.sea} 90%)`,
        textAlign: 'center'
      }}>
        <Typography
          variant="h4"
          fontWeight="bold"
          gutterBottom
          sx={{
            color: colors.cloud,
            fontSize: { xs: '1.5rem', sm: '2rem' }
          }}
        >
          Avaliações Físicas
        </Typography>
        <Typography
          variant="subtitle1"
          sx={{
            color: `${colors.cloud}CC`,
            fontSize: { xs: '0.875rem', sm: '1rem' }
          }}
        >
          Gerencie as avaliações físicas dos seus clientes e acompanhe o progresso
        </Typography>
      </Box>

      <Box sx={{ mb: { xs: 2, sm: 3 }, display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
        <Tabs 
          value={tabAtiva} 
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          sx={{
            '& .MuiTabs-indicator': {
              backgroundColor: colors.sea,
              height: 3,
            },
            '& .MuiTab-root': {
              fontWeight: 600,
              fontSize: { xs: '0.75rem', sm: '0.875rem', md: '1rem' },
              color: `${colors.ocean}99`,
              '&.Mui-selected': {
                color: colors.sea,
              },
              '&.Mui-disabled': {
                color: `${colors.ocean}4D`,
              },
            },
          }}
        >
          <Tab label="Lista de Avaliações" />
          <Tab label="Detalhes da Avaliação" disabled={!avaliacaoSelecionada && !modoEdicao} />
          <Tab label="Gráficos de Evolução" disabled={avaliacoes.length === 0} />
          <Tab label="Comparar Avaliações" disabled={avaliacoes.length < 2} />
        </Tabs>

        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleNovaAvaliacao}
          sx={{
            bgcolor: colors.sea,
            color: colors.cloud,
            borderRadius: 2,
            py: 1,
            px: 2,
            '&:hover': {
              bgcolor: colors.ocean,
              boxShadow: `0px 4px 8px ${colors.sea}33`
            },
          }}
        >
          Nova Avaliação
        </Button>
      </Box>

      {tabAtiva === 0 && (
        <Paper
          elevation={0}
          sx={{
            borderRadius: 3,
            border: `1px solid ${colors.sea}`,
            overflow: 'hidden',
            bgcolor: colors.cloud,
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)'
          }}
        >
          <Box sx={{ p: { xs: 2, sm: 3 } }}>
            <AvaliacaoFisicaList 
              avaliacoes={avaliacoes}
              carregando={carregando}
              onEditar={handleEditarAvaliacao}
              onVisualizar={handleVisualizarAvaliacao}
              onExcluir={excluirAvaliacao}
              onBuscar={buscarAvaliacoes}
            />
          </Box>
        </Paper>
      )}

      {tabAtiva === 1 && (
        <Box>
          {modoEdicao ? (
            <Paper
              elevation={0}
              sx={{
                p: { xs: 2, sm: 3 },
                borderRadius: 3,
                bgcolor: colors.cloud,
                boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)'
              }}
            >
              <AvaliacaoFisicaForm
                avaliacaoParaEditar={avaliacaoSelecionada || undefined}
                clienteId={clienteSelecionadoId || undefined}
                onSalvar={handleSalvarAvaliacao}
                onCancelar={handleCancelarEdicao}
              />
            </Paper>
          ) : avaliacaoSelecionada ? (
            <Card
              elevation={0}
              sx={{
                borderRadius: 3,
                overflow: 'hidden',
                border: `1px solid ${colors.sea}`,
                bgcolor: colors.cloud,
                boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)',
                '&:hover': {
                  borderColor: colors.sea,
                  boxShadow: `0px 8px 16px ${colors.sea}19`
                }
              }}
            >
              <Box sx={{ 
                bgcolor: `${colors.sea}0A`,
                borderBottom: `1px solid ${colors.sea}`,
                color: colors.ocean,
                p: { xs: 2, sm: 3 } 
              }}>
                <Typography
                  variant="h5"
                  fontWeight="bold"
                  sx={{ fontSize: '1.25rem' }}
                >
                  Detalhes da Avaliação Física
                </Typography>
              </Box>
              
              <Box sx={{ p: { xs: 2, sm: 3 } }}>
                <Grid container spacing={{ xs: 2, sm: 3 }}>
                  <Grid item xs={12} md={6}>
                    <List>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: `${colors.sea}1A`, color: colors.sea }}>
                            <PersonIcon />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="subtitle1" sx={{ color: colors.ocean, fontWeight: 500 }}>
                              Cliente
                            </Typography>
                          }
                          secondary={
                            <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                              {clientes.find(c => c.id === avaliacaoSelecionada?.cliente_id)?.nome || '-'}
                            </Typography>
                          }
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: `${colors.sea}1A`, color: colors.sea }}>
                            <CalendarTodayIcon />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="subtitle1" sx={{ color: colors.ocean, fontWeight: 500 }}>
                              Data da Avaliação
                            </Typography>
                          }
                          secondary={
                            <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                              {formatarData(avaliacaoSelecionada?.data_avaliacao || '')}
                            </Typography>
                          }
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: `${colors.sea}1A`, color: colors.sea }}>
                            <ScaleIcon />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="subtitle1" sx={{ color: colors.ocean, fontWeight: 500 }}>
                              Peso
                            </Typography>
                          }
                          secondary={
                            <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                              {avaliacaoSelecionada?.peso} kg
                            </Typography>
                          }
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: `${colors.sea}1A`, color: colors.sea }}>
                            <HeightIcon />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="subtitle1" sx={{ color: colors.ocean, fontWeight: 500 }}>
                              Altura
                            </Typography>
                          }
                          secondary={
                            <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                              {avaliacaoSelecionada?.altura} cm
                            </Typography>
                          }
                        />
                      </ListItem>
                    </List>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <List>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: `${colors.sea}1A`, color: colors.sea }}>
                            <CalculateIcon />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="subtitle1" sx={{ color: colors.ocean, fontWeight: 500 }}>
                                IMC
                              </Typography>
                              <Tooltip 
                                title="Índice de Massa Corporal = Peso / (Altura²)" 
                                arrow
                                placement="right"
                              >
                                <HelpOutlineIcon 
                                  sx={{ 
                                    fontSize: 18, 
                                    color: `${colors.ocean}4D`,
                                    cursor: 'help'
                                  }} 
                                />
                              </Tooltip>
                            </Box>
                          }
                          secondary={
                            <Stack spacing={1}>
                              <Typography 
                                variant="body2" 
                                sx={{ 
                                  color: `${colors.ocean}99`,
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 1
                                }}
                              >
                                {calcularIMC(
                                  avaliacaoSelecionada?.peso || 0,
                                  avaliacaoSelecionada?.altura || 0
                                )}
                              </Typography>
                              <Typography 
                                variant="caption" 
                                sx={{ 
                                  color: getCorIMC(calcularIMC(
                                    avaliacaoSelecionada?.peso || 0,
                                    avaliacaoSelecionada?.altura || 0
                                  )),
                                  fontWeight: 500
                                }}
                              >
                                {classificarIMC(calcularIMC(
                                  avaliacaoSelecionada?.peso || 0,
                                  avaliacaoSelecionada?.altura || 0
                                ))}
                              </Typography>
                            </Stack>
                          }
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: `${colors.sea}1A`, color: colors.sea }}>
                            <MonitorWeightIcon />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="subtitle1" sx={{ color: colors.ocean, fontWeight: 500 }}>
                              Percentual de Gordura
                            </Typography>
                          }
                          secondary={
                            <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                              {avaliacaoSelecionada?.dobras_cutaneas?.percentual_gordura || 0}%
                            </Typography>
                          }
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: `${colors.sea}1A`, color: colors.sea }}>
                            <StraightenIcon />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="subtitle1" sx={{ color: colors.ocean, fontWeight: 500 }}>
                              Circunferência Abdominal
                            </Typography>
                          }
                          secondary={
                            <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                              {avaliacaoSelecionada?.medidas_antropometricas?.abdomen || 0} cm
                            </Typography>
                          }
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: `${colors.sea}1A`, color: colors.sea }}>
                            <FitnessCenterIcon />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="subtitle1" sx={{ color: colors.ocean, fontWeight: 500 }}>
                              Massa Muscular
                            </Typography>
                          }
                          secondary={
                            <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                              {calcularMassaMuscular(
                                avaliacaoSelecionada?.peso || 0,
                                avaliacaoSelecionada?.dobras_cutaneas?.percentual_gordura || 0
                              )} kg
                            </Typography>
                          }
                        />
                      </ListItem>
                    </List>
                  </Grid>
                </Grid>
              </Box>
              <Box sx={{ p: { xs: 2, sm: 3 }, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                <Button
                  variant="contained"
                  onClick={() => handleEditarAvaliacao(avaliacaoSelecionada)}
                  sx={{
                    bgcolor: colors.sea,
                    borderRadius: 3,
                    '&:hover': {
                      bgcolor: colors.ocean,
                      boxShadow: `0px 4px 8px ${colors.sea}33`
                    },
                  }}
                >
                  Editar
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => setTabAtiva(0)}
                  sx={{
                    borderRadius: 3,
                    borderColor: colors.sea,
                    color: colors.sea,
                    '&:hover': {
                      borderColor: colors.ocean,
                      color: colors.ocean,
                      bgcolor: `${colors.sea}0A`
                    },
                  }}
                >
                  Voltar para Lista
                </Button>
              </Box>
            </Card>
          ) : (
            <Typography>Nenhuma avaliação selecionada</Typography>
          )}
        </Box>
      )}

      {tabAtiva === 2 && (
        <Paper 
          elevation={0} 
          sx={{ 
            p: 3, 
            borderRadius: 4,
            bgcolor: colors.cloud,
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)'
          }}
        >
          <EvolucaoGraficos avaliacoes={avaliacoes} />
        </Paper>
      )}

      {tabAtiva === 3 && (
        <Paper 
          elevation={0} 
          sx={{ 
            p: 3, 
            borderRadius: 4,
            bgcolor: colors.cloud,
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)'
          }}
        >
          <ComparacaoAvaliacoes avaliacoes={avaliacoes} />
        </Paper>
      )}

      {/* Diálogo para selecionar cliente */}
      <Dialog
        open={dialogClienteAberto}
        onClose={() => setDialogClienteAberto(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
          }
        }}
      >
        <DialogTitle sx={{ color: colors.ocean, fontWeight: 600, pb: 1 }}>
          Selecionar Cliente
        </DialogTitle>
        <DialogContent>
          <List>
            {clientes.map((cliente) => (
              <ListItem
                key={cliente.id}
                button
                onClick={() => handleSelecionarCliente(cliente.id)}
                sx={{
                  borderRadius: 2,
                  mb: 1,
                  '&:hover': {
                    bgcolor: `${colors.sea}0A`,
                  }
                }}
              >
                <ListItemIcon>
                  <Avatar sx={{ bgcolor: `${colors.sea}1A`, color: colors.sea }}>
                    {cliente.nome.charAt(0).toUpperCase()}
                  </Avatar>
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography sx={{ color: colors.ocean, fontWeight: 500 }}>
                      {cliente.nome}
                    </Typography>
                  }
                  secondary={
                    <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                      {cliente.email}
                    </Typography>
                  }
                />
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions sx={{ p: 2, pt: 0 }}>
          <Button 
            onClick={() => setDialogClienteAberto(false)}
            sx={{ 
              color: `${colors.ocean}99`,
              '&:hover': {
                bgcolor: `${colors.ocean}0A`,
                color: colors.ocean
              }
            }}
          >
            Cancelar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo de detalhes da avaliação */}
      <Dialog
        open={dialogDetalhesAberto}
        onClose={() => setDialogDetalhesAberto(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            overflow: 'hidden',
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)'
          }
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: `${colors.sea}0A`,
          borderBottom: `1px solid ${colors.sea}`,
          color: colors.ocean,
          p: 3 
        }}>
          Detalhes da Avaliação Física
        </DialogTitle>
        <DialogContent dividers>
          {avaliacaoSelecionada && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <Avatar sx={{ bgcolor: '#0038FF' }}>
                        <PersonIcon />
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography fontWeight="bold">Cliente</Typography>}
                      secondary={clientes.find(c => c.id === avaliacaoSelecionada.cliente_id)?.nome || 'Cliente não encontrado'}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <Avatar sx={{ bgcolor: '#0038FF' }}>
                        <CalendarTodayIcon />
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography fontWeight="bold">Data da Avaliação</Typography>}
                      secondary={formatarData(avaliacaoSelecionada.data_avaliacao)}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <Avatar sx={{ bgcolor: '#0038FF' }}>
                        <ScaleIcon />
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography fontWeight="bold">Peso</Typography>}
                      secondary={`${avaliacaoSelecionada.peso} kg`}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <Avatar sx={{ bgcolor: '#0038FF' }}>
                        <HeightIcon />
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography fontWeight="bold">Altura</Typography>}
                      secondary={`${avaliacaoSelecionada.altura} cm`}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <Avatar sx={{ 
                        bgcolor: getCorIMC(calcularIMC(avaliacaoSelecionada.peso, avaliacaoSelecionada.altura)) 
                      }}>
                        <CalculateIcon />
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography fontWeight="bold">IMC</Typography>}
                      secondary={
                        <Typography>
                          <strong>{calcularIMC(avaliacaoSelecionada.peso, avaliacaoSelecionada.altura)}</strong> ({classificarIMC(calcularIMC(avaliacaoSelecionada.peso, avaliacaoSelecionada.altura))})
                        </Typography>
                      }
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <Avatar sx={{ bgcolor: '#0038FF' }}>
                        <FitnessCenterIcon />
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography fontWeight="bold">Percentual de Gordura</Typography>}
                      secondary={avaliacaoSelecionada.dobras_cutaneas?.percentual_gordura
                        ? `${avaliacaoSelecionada.dobras_cutaneas.percentual_gordura}%`
                        : 'Não calculado'}
                    />
                  </ListItem>
                </List>
              </Grid>

              {avaliacaoSelecionada.dobras_cutaneas && (
                <Grid item xs={12} md={6}>
                  <Paper 
                    elevation={0} 
                    sx={{ 
                      p: 2, 
                      borderRadius: 3,
                      height: '100%',
                      bgcolor: `${colors.sea}0A`,
                      border: `1px solid ${colors.sea}`
                    }}
                  >
                    <Stack direction="row" alignItems="center" spacing={1} mb={2}>
                      <MonitorWeightIcon sx={{ color: colors.sea }} />
                      <Typography variant="h6" fontWeight="bold" sx={{ color: colors.ocean }}>
                        Dobras Cutâneas
                      </Typography>
                      <Tooltip title="Medidas em milímetros (mm)">
                        <IconButton size="small">
                          <HelpOutlineIcon />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                    <Divider sx={{ mb: 2 }} />
                    <List dense>
                      {Object.entries(avaliacaoSelecionada.dobras_cutaneas)
                        .filter(([key]) => !['id', 'avaliacao_id', 'percentual_gordura'].includes(key))
                        .map(([key, value]) => (
                          value && (
                            <ListItem key={key}>
                              <ListItemText
                                primary={<Typography fontWeight="medium">{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</Typography>}
                                secondary={`${value} mm`}
                              />
                            </ListItem>
                          )
                        ))}
                    </List>
                  </Paper>
                </Grid>
              )}

              {avaliacaoSelecionada.medidas_antropometricas && (
                <Grid item xs={12}>
                  <Paper 
                    elevation={2} 
                    sx={{ 
                      p: 2, 
                      borderRadius: 3,
                      bgcolor: '#f8f9fa'
                    }}
                  >
                    <Stack direction="row" alignItems="center" spacing={1} mb={2}>
                      <StraightenIcon sx={{ color: '#0038FF' }} />
                      <Typography variant="h6" fontWeight="bold">
                        Medidas Antropométricas
                      </Typography>
                      <Tooltip title="Medidas em centímetros (cm)">
                        <IconButton size="small">
                          <HelpOutlineIcon />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                    <Divider sx={{ mb: 2 }} />
                    <Grid container spacing={2}>
                      {Object.entries(avaliacaoSelecionada.medidas_antropometricas)
                        .filter(([key]) => !['id', 'avaliacao_id'].includes(key))
                        .map(([key, value]) => (
                          value && (
                            <Grid item xs={12} sm={6} md={4} key={key}>
                              <Paper 
                                elevation={1} 
                                sx={{ 
                                  p: 1, 
                                  borderRadius: 2,
                                  bgcolor: 'white'
                                }}
                              >
                                <ListItem>
                                  <ListItemText
                                    primary={<Typography fontWeight="medium">{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</Typography>}
                                    secondary={`${value} cm`}
                                  />
                                </ListItem>
                              </Paper>
                            </Grid>
                          )
                        ))}
                    </Grid>
                  </Paper>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2, bgcolor: colors.cloud }}>
          <Button
            variant="contained"
            onClick={() => {
              setDialogDetalhesAberto(false);
              handleEditarAvaliacao(avaliacaoSelecionada!);
            }}
            sx={{
              bgcolor: colors.sea,
              borderRadius: 3,
              '&:hover': {
                bgcolor: colors.ocean,
                boxShadow: `0px 4px 8px ${colors.sea}33`
              },
            }}
          >
            Editar
          </Button>
          <Button
            variant="outlined"
            onClick={() => setDialogDetalhesAberto(false)}
            sx={{
              borderRadius: 3,
              borderColor: colors.sea,
              color: colors.sea,
              '&:hover': {
                borderColor: colors.ocean,
                color: colors.ocean,
                bgcolor: `${colors.sea}0A`
              },
            }}
          >
            Fechar
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AvaliacoesPage; 