import React from 'react';
import { Box, Fade, Slide, BoxProps } from '@mui/material';
import { keyframes } from '@mui/system';
import { colors } from '../../styles/colors';

// ============================================================================
// ANIMATED COMPONENTS - MICRO-ANIMAÇÕES PROFISSIONAIS
// ============================================================================

// Keyframes para animações customizadas
const fadeInUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const fadeInLeft = keyframes`
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const fadeInRight = keyframes`
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const scaleIn = keyframes`
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
`;

const slideInDown = keyframes`
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

const shimmer = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`;

const float = keyframes`
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
`;

// ============================================================================
// COMPONENTES ANIMADOS
// ============================================================================

interface AnimatedBoxProps extends BoxProps {
  animation?: 'fadeInUp' | 'fadeInLeft' | 'fadeInRight' | 'scaleIn' | 'slideInDown' | 'pulse' | 'float';
  duration?: number;
  delay?: number;
  children: React.ReactNode;
}

export const AnimatedBox: React.FC<AnimatedBoxProps> = ({
  animation = 'fadeInUp',
  duration = 0.6,
  delay = 0,
  children,
  sx,
  ...props
}) => {
  const getAnimation = () => {
    switch (animation) {
      case 'fadeInUp': return fadeInUp;
      case 'fadeInLeft': return fadeInLeft;
      case 'fadeInRight': return fadeInRight;
      case 'scaleIn': return scaleIn;
      case 'slideInDown': return slideInDown;
      case 'pulse': return pulse;
      case 'float': return float;
      default: return fadeInUp;
    }
  };

  return (
    <Box
      sx={{
        animation: `${getAnimation()} ${duration}s ease-out ${delay}s both`,
        ...sx
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

// ============================================================================
// TRANSIÇÕES DE PÁGINA
// ============================================================================

interface PageTransitionProps {
  children: React.ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right';
  duration?: number;
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  direction = 'up',
  duration = 300
}) => {
  return (
    <Slide direction={direction} in={true} timeout={duration}>
      <Box>
        {children}
      </Box>
    </Slide>
  );
};

// ============================================================================
// CONTAINER ANIMADO PARA LISTAS
// ============================================================================

interface AnimatedListProps {
  children: React.ReactNode;
  staggerDelay?: number;
}

export const AnimatedList: React.FC<AnimatedListProps> = ({
  children,
  staggerDelay = 0.1
}) => {
  const childrenArray = React.Children.toArray(children);

  return (
    <Box>
      {childrenArray.map((child, index) => (
        <AnimatedBox
          key={index}
          animation="fadeInUp"
          delay={index * staggerDelay}
          duration={0.6}
        >
          {child}
        </AnimatedBox>
      ))}
    </Box>
  );
};

// ============================================================================
// BOTÃO COM HOVER ANIMADO
// ============================================================================

interface AnimatedButtonProps extends BoxProps {
  children: React.ReactNode;
  hoverScale?: number;
  hoverColor?: string;
  disabled?: boolean;
  bounceOnHover?: boolean;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  hoverScale = 1.05,
  hoverColor,
  disabled = false,
  bounceOnHover = false,
  sx,
  ...props
}) => {
  return (
    <Box
      sx={{
        cursor: disabled ? 'not-allowed' : 'pointer',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        opacity: disabled ? 0.6 : 1,
        pointerEvents: disabled ? 'none' : 'auto',
        '&:hover': !disabled ? {
          transform: bounceOnHover ? 'translateY(-2px)' : `scale(${hoverScale})`,
          color: hoverColor,
          filter: 'brightness(1.1)',
        } : {},
        '&:active': !disabled ? {
          transform: bounceOnHover ? 'translateY(0px)' : `scale(${hoverScale * 0.95})`,
        } : {},
        ...sx
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

// ============================================================================
// CARD COM ANIMAÇÕES
// ============================================================================

interface AnimatedCardProps extends BoxProps {
  children: React.ReactNode;
  hoverElevation?: boolean;
  glowColor?: string;
}

export const AnimatedCard: React.FC<AnimatedCardProps> = ({
  children,
  hoverElevation = true,
  glowColor = colors.professional[500],
  sx,
  ...props
}) => {
  return (
    <Box
      sx={{
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        borderRadius: 3,
        ...(hoverElevation && {
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: `0 8px 25px ${glowColor}20, 0 4px 10px ${glowColor}15`,
            '& .card-content': {
              transform: 'scale(1.02)',
            }
          }
        }),
        ...sx
      }}
      {...props}
    >
      <Box
        className="card-content"
        sx={{
          transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

// ============================================================================
// LOADING SHIMMER
// ============================================================================

interface ShimmerProps extends BoxProps {
  width?: string | number;
  height?: string | number;
  borderRadius?: number;
}

export const Shimmer: React.FC<ShimmerProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  sx,
  ...props
}) => {
  return (
    <Box
      sx={{
        width,
        height,
        borderRadius,
        background: `linear-gradient(90deg, ${colors.professional[100]} 0%, ${colors.professional[200]} 50%, ${colors.professional[100]} 100%)`,
        backgroundSize: '200px 100%',
        animation: `${shimmer} 1.5s ease-in-out infinite`,
        ...sx
      }}
      {...props}
    />
  );
};

// ============================================================================
// FADE IN QUANDO VISÍVEL
// ============================================================================

interface FadeInWhenVisibleProps {
  children: React.ReactNode;
  threshold?: number;
  delay?: number;
}

export const FadeInWhenVisible: React.FC<FadeInWhenVisibleProps> = ({
  children,
  threshold = 0.1,
  delay = 0
}) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const ref = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => setIsVisible(true), delay * 1000);
        }
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold, delay]);

  return (
    <Box ref={ref}>
      <Fade in={isVisible} timeout={600}>
        <Box>
          {children}
        </Box>
      </Fade>
    </Box>
  );
};

// ============================================================================
// CONTADOR ANIMADO
// ============================================================================

interface AnimatedCounterProps {
  value: number;
  duration?: number;
  prefix?: string;
  suffix?: string;
  decimals?: number;
}

export const AnimatedCounter: React.FC<AnimatedCounterProps> = ({
  value,
  duration = 2000,
  prefix = '',
  suffix = '',
  decimals = 0
}) => {
  const [count, setCount] = React.useState(0);

  React.useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      setCount(value * easeOutQuart);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => cancelAnimationFrame(animationFrame);
  }, [value, duration]);

  return (
    <span>
      {prefix}{count.toFixed(decimals)}{suffix}
    </span>
  );
};

// ============================================================================
// PROGRESS BAR ANIMADA
// ============================================================================

interface AnimatedProgressProps extends BoxProps {
  value: number;
  max?: number;
  color?: string;
  height?: number;
  showValue?: boolean;
  duration?: number;
}

export const AnimatedProgress: React.FC<AnimatedProgressProps> = ({
  value,
  max = 100,
  color = colors.energy[500],
  height = 8,
  showValue = false,
  duration = 1000,
  sx,
  ...props
}) => {
  const [animatedValue, setAnimatedValue] = React.useState(0);
  const percentage = Math.min((value / max) * 100, 100);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedValue(percentage);
    }, 100);

    return () => clearTimeout(timer);
  }, [percentage]);

  return (
    <Box sx={{ width: '100%', ...sx }} {...props}>
      <Box
        sx={{
          width: '100%',
          height,
          bgcolor: `${color}20`,
          borderRadius: height / 2,
          overflow: 'hidden',
          position: 'relative'
        }}
      >
        <Box
          sx={{
            width: `${animatedValue}%`,
            height: '100%',
            bgcolor: color,
            borderRadius: height / 2,
            transition: `width ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`,
            position: 'relative',
            '&::after': showValue ? {
              content: `"${Math.round(animatedValue)}%"`,
              position: 'absolute',
              right: 8,
              top: '50%',
              transform: 'translateY(-50%)',
              fontSize: '0.75rem',
              color: 'white',
              fontWeight: 600
            } : {}
          }}
        />
      </Box>
    </Box>
  );
};

// ============================================================================
// BOTÃO FAB ANIMADO COM RIPPLE EFFECT
// ============================================================================

interface AnimatedFabProps extends BoxProps {
  children: React.ReactNode;
  color?: string;
  size?: 'small' | 'medium' | 'large';
  pulseOnHover?: boolean;
  rippleEffect?: boolean;
  onClick?: () => void;
}

export const AnimatedFab: React.FC<AnimatedFabProps> = ({
  children,
  color = colors.energy[500],
  size = 'medium',
  pulseOnHover = true,
  rippleEffect = true,
  onClick,
  sx,
  ...props
}) => {
  const [ripples, setRipples] = React.useState<Array<{ id: number; x: number; y: number }>>([]);
  const fabRef = React.useRef<HTMLDivElement>(null);

  const getSizeProps = () => {
    switch (size) {
      case 'small': return { width: 40, height: 40, fontSize: '1rem' };
      case 'large': return { width: 64, height: 64, fontSize: '1.5rem' };
      default: return { width: 56, height: 56, fontSize: '1.25rem' };
    }
  };

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (rippleEffect && fabRef.current) {
      const rect = fabRef.current.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      const newRipple = { id: Date.now(), x, y };
      setRipples(prev => [...prev, newRipple]);

      setTimeout(() => {
        setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
      }, 600);
    }

    onClick?.();
  };

  const sizeProps = getSizeProps();

  return (
    <Box
      ref={fabRef}
      onClick={handleClick}
      sx={{
        ...sizeProps,
        borderRadius: '50%',
        bgcolor: color,
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        position: 'relative',
        overflow: 'hidden',
        boxShadow: `0 4px 12px ${color}40`,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        '&:hover': {
          transform: pulseOnHover ? 'scale(1.1)' : 'translateY(-2px)',
          boxShadow: `0 8px 20px ${color}50`,
          filter: 'brightness(1.1)',
        },
        '&:active': {
          transform: 'scale(0.95)',
        },
        ...sx
      }}
      {...props}
    >
      {children}

      {/* Ripple effects */}
      {ripples.map(ripple => (
        <Box
          key={ripple.id}
          sx={{
            position: 'absolute',
            left: ripple.x,
            top: ripple.y,
            width: 0,
            height: 0,
            borderRadius: '50%',
            bgcolor: 'rgba(255, 255, 255, 0.6)',
            transform: 'translate(-50%, -50%)',
            animation: 'ripple 0.6s ease-out',
            '@keyframes ripple': {
              to: {
                width: '100px',
                height: '100px',
                opacity: 0,
              }
            }
          }}
        />
      ))}
    </Box>
  );
};

// ============================================================================
// TOOLTIP ANIMADO
// ============================================================================

interface AnimatedTooltipProps {
  children: React.ReactNode;
  title: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
}

export const AnimatedTooltip: React.FC<AnimatedTooltipProps> = ({
  children,
  title,
  placement = 'top',
  delay = 500
}) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const [timeoutId, setTimeoutId] = React.useState<NodeJS.Timeout | null>(null);

  const handleMouseEnter = () => {
    const id = setTimeout(() => setIsVisible(true), delay);
    setTimeoutId(id);
  };

  const handleMouseLeave = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
    setIsVisible(false);
  };

  const getTooltipPosition = () => {
    switch (placement) {
      case 'bottom': return { top: '100%', left: '50%', transform: 'translateX(-50%)', mt: 1 };
      case 'left': return { right: '100%', top: '50%', transform: 'translateY(-50%)', mr: 1 };
      case 'right': return { left: '100%', top: '50%', transform: 'translateY(-50%)', ml: 1 };
      default: return { bottom: '100%', left: '50%', transform: 'translateX(-50%)', mb: 1 };
    }
  };

  return (
    <Box
      sx={{ position: 'relative', display: 'inline-block' }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}

      <Fade in={isVisible} timeout={200}>
        <Box
          sx={{
            position: 'absolute',
            ...getTooltipPosition(),
            bgcolor: colors.professional[800],
            color: 'white',
            px: 1.5,
            py: 0.75,
            borderRadius: 1,
            fontSize: '0.75rem',
            fontWeight: 500,
            whiteSpace: 'nowrap',
            zIndex: 1000,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            '&::before': {
              content: '""',
              position: 'absolute',
              width: 0,
              height: 0,
              border: '4px solid transparent',
              ...(placement === 'top' && {
                top: '100%',
                left: '50%',
                transform: 'translateX(-50%)',
                borderTopColor: colors.professional[800],
              }),
              ...(placement === 'bottom' && {
                bottom: '100%',
                left: '50%',
                transform: 'translateX(-50%)',
                borderBottomColor: colors.professional[800],
              }),
              ...(placement === 'left' && {
                left: '100%',
                top: '50%',
                transform: 'translateY(-50%)',
                borderLeftColor: colors.professional[800],
              }),
              ...(placement === 'right' && {
                right: '100%',
                top: '50%',
                transform: 'translateY(-50%)',
                borderRightColor: colors.professional[800],
              }),
            }
          }}
        >
          {title}
        </Box>
      </Fade>
    </Box>
  );
};

export default AnimatedBox;
