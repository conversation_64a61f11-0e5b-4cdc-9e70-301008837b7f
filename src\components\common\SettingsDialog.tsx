import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tabs,
  Tab,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Slider,
  Divider,
  Alert,
  IconButton,
  Tooltip,
  Card,
  CardContent,
  Grid,
  Chip
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Palette as PaletteIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Backup as BackupIcon,
  Business as BusinessIcon,
  Close as CloseIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon,
  Brightness4 as AutoModeIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  RestartAlt as ResetIcon
} from '@mui/icons-material';
import { useSettings, useTheme, useNotifications, useBackupSettings, useClinicInfo } from '../../contexts/SettingsContext';
import { colors } from '../../styles/colors';
import { AnimatedBox, AnimatedButton } from './AnimatedComponents';

// ============================================================================
// SETTINGS DIALOG - INTERFACE DE CONFIGURAÇÕES DO SISTEMA
// ============================================================================

interface SettingsDialogProps {
  open: boolean;
  onClose: () => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div hidden={value !== index} style={{ height: '100%' }}>
      {value === index && (
        <Box sx={{ py: 2, height: '100%' }}>
          {children}
        </Box>
      )}
    </div>
  );
};

export const SettingsDialog: React.FC<SettingsDialogProps> = ({ open, onClose }) => {
  const { settings, updateSettings, resetSettings, exportSettings, importSettings } = useSettings();
  const { theme, isDarkMode, toggleTheme, primaryColor, fontSize, compactMode, showAnimations } = useTheme();
  const { notifications, emailNotifications, reminderNotifications, updateNotifications } = useNotifications();
  const { autoBackup, backupFrequency, updateBackupSettings } = useBackupSettings();
  const { clinicName, clinicAddress, clinicPhone, userName, userEmail, updateClinicInfo } = useClinicInfo();
  
  const [activeTab, setActiveTab] = useState(0);
  const [showResetConfirm, setShowResetConfirm] = useState(false);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleExportSettings = () => {
    const settingsJson = exportSettings();
    const blob = new Blob([settingsJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `personal_trainer_settings_${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  };

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      if (importSettings(content)) {
        alert('Configurações importadas com sucesso!');
      } else {
        alert('Erro ao importar configurações. Verifique o arquivo.');
      }
    };
    reader.readAsText(file);
  };

  const handleReset = () => {
    resetSettings();
    setShowResetConfirm(false);
    alert('Configurações restauradas para o padrão!');
  };

  const getThemeIcon = () => {
    switch (theme) {
      case 'dark': return <DarkModeIcon />;
      case 'light': return <LightModeIcon />;
      default: return <AutoModeIcon />;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 4,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
          height: '80vh'
        }
      }}
    >
      <DialogTitle sx={{ 
        bgcolor: colors.professional[50], 
        color: colors.professional[700],
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        pb: 0
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <SettingsIcon />
          Configurações do Sistema
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={activeTab} onChange={handleTabChange} variant="scrollable" scrollButtons="auto">
          <Tab icon={<PaletteIcon />} label="Aparência" />
          <Tab icon={<NotificationsIcon />} label="Notificações" />
          <Tab icon={<BackupIcon />} label="Backup" />
          <Tab icon={<BusinessIcon />} label="Clínica" />
          <Tab icon={<SecurityIcon />} label="Privacidade" />
        </Tabs>
      </Box>

      <DialogContent sx={{ p: 0, height: '100%', overflow: 'hidden' }}>
        {/* Aba Aparência */}
        <TabPanel value={activeTab} index={0}>
          <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
            <AnimatedBox animation="fadeInUp" delay={0.1}>
              <Card elevation={0} sx={{ mb: 3, border: `1px solid ${colors.professional[200]}` }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, color: colors.professional[700] }}>
                    Tema
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Card 
                        sx={{ 
                          cursor: 'pointer',
                          border: theme === 'light' ? `2px solid ${colors.energy[500]}` : `1px solid ${colors.professional[200]}`,
                          '&:hover': { borderColor: colors.energy[400] }
                        }}
                        onClick={() => updateSettings({ theme: 'light' })}
                      >
                        <CardContent sx={{ textAlign: 'center', py: 2 }}>
                          <LightModeIcon sx={{ fontSize: 32, color: colors.energy[500], mb: 1 }} />
                          <Typography variant="body2">Claro</Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                    
                    <Grid item xs={12} sm={4}>
                      <Card 
                        sx={{ 
                          cursor: 'pointer',
                          border: theme === 'dark' ? `2px solid ${colors.energy[500]}` : `1px solid ${colors.professional[200]}`,
                          '&:hover': { borderColor: colors.energy[400] }
                        }}
                        onClick={() => updateSettings({ theme: 'dark' })}
                      >
                        <CardContent sx={{ textAlign: 'center', py: 2 }}>
                          <DarkModeIcon sx={{ fontSize: 32, color: colors.professional[600], mb: 1 }} />
                          <Typography variant="body2">Escuro</Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                    
                    <Grid item xs={12} sm={4}>
                      <Card 
                        sx={{ 
                          cursor: 'pointer',
                          border: theme === 'auto' ? `2px solid ${colors.energy[500]}` : `1px solid ${colors.professional[200]}`,
                          '&:hover': { borderColor: colors.energy[400] }
                        }}
                        onClick={() => updateSettings({ theme: 'auto' })}
                      >
                        <CardContent sx={{ textAlign: 'center', py: 2 }}>
                          <AutoModeIcon sx={{ fontSize: 32, color: colors.health[500], mb: 1 }} />
                          <Typography variant="body2">Automático</Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </AnimatedBox>

            <AnimatedBox animation="fadeInUp" delay={0.2}>
              <Card elevation={0} sx={{ mb: 3, border: `1px solid ${colors.professional[200]}` }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, color: colors.professional[700] }}>
                    Interface
                  </Typography>
                  
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="body2" sx={{ mb: 1, color: colors.professional[600] }}>
                      Tamanho da Fonte
                    </Typography>
                    <FormControl size="small" sx={{ minWidth: 120 }}>
                      <Select
                        value={fontSize}
                        onChange={(e) => updateSettings({ fontSize: e.target.value as any })}
                      >
                        <MenuItem value="small">Pequena</MenuItem>
                        <MenuItem value="medium">Média</MenuItem>
                        <MenuItem value="large">Grande</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={compactMode}
                        onChange={(e) => updateSettings({ compactMode: e.target.checked })}
                      />
                    }
                    label="Modo Compacto"
                    sx={{ mb: 2 }}
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={showAnimations}
                        onChange={(e) => updateSettings({ showAnimations: e.target.checked })}
                      />
                    }
                    label="Animações"
                  />
                </CardContent>
              </Card>
            </AnimatedBox>
          </Box>
        </TabPanel>

        {/* Aba Notificações */}
        <TabPanel value={activeTab} index={1}>
          <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
            <AnimatedBox animation="fadeInUp" delay={0.1}>
              <Card elevation={0} sx={{ mb: 3, border: `1px solid ${colors.professional[200]}` }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, color: colors.professional[700] }}>
                    Notificações
                  </Typography>
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notifications}
                        onChange={(e) => updateNotifications({ notifications: e.target.checked })}
                      />
                    }
                    label="Ativar Notificações"
                    sx={{ mb: 2 }}
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={reminderNotifications}
                        onChange={(e) => updateNotifications({ reminderNotifications: e.target.checked })}
                        disabled={!notifications}
                      />
                    }
                    label="Lembretes de Avaliação"
                    sx={{ mb: 2 }}
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={emailNotifications}
                        onChange={(e) => updateNotifications({ emailNotifications: e.target.checked })}
                        disabled={!notifications}
                      />
                    }
                    label="Notificações por Email"
                  />
                </CardContent>
              </Card>
            </AnimatedBox>
          </Box>
        </TabPanel>

        {/* Aba Backup */}
        <TabPanel value={activeTab} index={2}>
          <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
            <AnimatedBox animation="fadeInUp" delay={0.1}>
              <Card elevation={0} sx={{ mb: 3, border: `1px solid ${colors.professional[200]}` }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, color: colors.professional[700] }}>
                    Backup Automático
                  </Typography>
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={autoBackup}
                        onChange={(e) => updateBackupSettings({ autoBackup: e.target.checked })}
                      />
                    }
                    label="Ativar Backup Automático"
                    sx={{ mb: 2 }}
                  />

                  <Box sx={{ mb: 3 }}>
                    <Typography variant="body2" sx={{ mb: 1, color: colors.professional[600] }}>
                      Frequência do Backup
                    </Typography>
                    <FormControl size="small" sx={{ minWidth: 120 }} disabled={!autoBackup}>
                      <Select
                        value={backupFrequency}
                        onChange={(e) => updateBackupSettings({ backupFrequency: e.target.value as any })}
                      >
                        <MenuItem value="daily">Diário</MenuItem>
                        <MenuItem value="weekly">Semanal</MenuItem>
                        <MenuItem value="monthly">Mensal</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                </CardContent>
              </Card>
            </AnimatedBox>
          </Box>
        </TabPanel>

        {/* Aba Clínica */}
        <TabPanel value={activeTab} index={3}>
          <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
            <AnimatedBox animation="fadeInUp" delay={0.1}>
              <Card elevation={0} sx={{ mb: 3, border: `1px solid ${colors.professional[200]}` }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, color: colors.professional[700] }}>
                    Informações da Clínica
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        size="small"
                        label="Nome da Clínica"
                        value={clinicName}
                        onChange={(e) => updateClinicInfo({ clinicName: e.target.value })}
                        sx={{ mb: 2 }}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        size="small"
                        label="Telefone"
                        value={clinicPhone}
                        onChange={(e) => updateClinicInfo({ clinicPhone: e.target.value })}
                        sx={{ mb: 2 }}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        size="small"
                        label="Endereço"
                        value={clinicAddress}
                        onChange={(e) => updateClinicInfo({ clinicAddress: e.target.value })}
                        sx={{ mb: 2 }}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        size="small"
                        label="Seu Nome"
                        value={userName}
                        onChange={(e) => updateClinicInfo({ userName: e.target.value })}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        size="small"
                        label="Seu Email"
                        type="email"
                        value={userEmail}
                        onChange={(e) => updateClinicInfo({ userEmail: e.target.value })}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </AnimatedBox>
          </Box>
        </TabPanel>

        {/* Aba Privacidade */}
        <TabPanel value={activeTab} index={4}>
          <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
            <AnimatedBox animation="fadeInUp" delay={0.1}>
              <Card elevation={0} sx={{ mb: 3, border: `1px solid ${colors.professional[200]}` }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, color: colors.professional[700] }}>
                    Dados e Privacidade
                  </Typography>
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.analyticsEnabled}
                        onChange={(e) => updateSettings({ analyticsEnabled: e.target.checked })}
                      />
                    }
                    label="Permitir Analytics"
                    sx={{ mb: 2 }}
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.crashReporting}
                        onChange={(e) => updateSettings({ crashReporting: e.target.checked })}
                      />
                    }
                    label="Relatórios de Erro"
                  />
                </CardContent>
              </Card>
            </AnimatedBox>
          </Box>
        </TabPanel>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0, gap: 1 }}>
        <input
          accept=".json"
          style={{ display: 'none' }}
          id="import-settings"
          type="file"
          onChange={handleImportSettings}
        />
        
        <Tooltip title="Exportar Configurações">
          <IconButton onClick={handleExportSettings}>
            <DownloadIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Importar Configurações">
          <label htmlFor="import-settings">
            <IconButton component="span">
              <UploadIcon />
            </IconButton>
          </label>
        </Tooltip>
        
        <Tooltip title="Restaurar Padrões">
          <IconButton onClick={() => setShowResetConfirm(true)}>
            <ResetIcon />
          </IconButton>
        </Tooltip>
        
        <Box sx={{ flexGrow: 1 }} />
        
        <Button onClick={onClose}>
          Fechar
        </Button>
      </DialogActions>

      {/* Dialog de confirmação de reset */}
      <Dialog open={showResetConfirm} onClose={() => setShowResetConfirm(false)}>
        <DialogTitle>Restaurar Configurações Padrão</DialogTitle>
        <DialogContent>
          <Typography>
            Tem certeza que deseja restaurar todas as configurações para os valores padrão? 
            Esta ação não pode ser desfeita.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowResetConfirm(false)}>Cancelar</Button>
          <Button onClick={handleReset} color="error" variant="contained">
            Restaurar
          </Button>
        </DialogActions>
      </Dialog>
    </Dialog>
  );
};

export default SettingsDialog;
