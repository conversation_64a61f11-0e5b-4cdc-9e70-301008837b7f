import { createTheme } from '@mui/material/styles';
import { colors } from './colors';

export const theme = createTheme({
  palette: {
    primary: {
      main: colors.primary, // #8A2BE2 - Roxo elegante
      light: colors.purple[300], // #BA68C8 - Roxo médio claro
      dark: colors.purple[700], // #7B1FA2 - Roxo mais escuro
      contrastText: colors.white,
    },
    secondary: {
      main: colors.secondary, // #FFFFFF - Branco puro
      light: colors.neutral[100], // #F5F5F5 - Cinza muito claro
      dark: colors.neutral[300], // #E0E0E0 - Cinza suave
      contrastText: colors.text.primary,
    },
    error: {
      main: colors.error, // #F44336 - Vermelho suave
      light: '#EF9A9A',
      dark: '#D32F2F',
    },
    warning: {
      main: colors.warning, // #FF9800 - Laranja suave
      light: '#FFCC80',
      dark: '#F57C00',
    },
    info: {
      main: colors.info, // #2196F3 - Azul informativo
      light: '#81D4FA',
      dark: '#1976D2',
    },
    success: {
      main: colors.success, // #4CAF50 - Verde natural
      light: colors.functional.success, // #4CAF50 - Verde sucesso
      dark: '#388E3C', // Verde escuro
    },
    background: {
      default: colors.neutral[50], // #FFFFFF - Branco puro
      paper: colors.secondary, // #FFFFFF - Branco puro
    },
    text: {
      primary: colors.text.primary, // #212121 - Cinza muito escuro
      secondary: colors.text.secondary, // #616161 - Cinza escuro
      disabled: colors.text.disabled, // #BDBDBD - Cinza claro
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 700,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    subtitle1: {
      fontSize: '1rem',
      lineHeight: 1.5,
      letterSpacing: '0.00938em',
    },
    subtitle2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
      letterSpacing: '0.00714em',
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
      letterSpacing: '0.00938em',
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.6,
      letterSpacing: '0.01071em',
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 600,
      letterSpacing: '0.02857em',
      textTransform: 'none',
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          textTransform: 'none',
          fontWeight: 600,
          padding: '12px 24px',
          fontSize: '0.875rem',
          boxShadow: 'none',
          transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
        },
        contained: {
          '&:hover': {
            boxShadow: colors.shadows.md,
            transform: 'translateY(-2px)',
          },
          '&:active': {
            transform: 'translateY(0px)',
          },
        },
        containedPrimary: {
          background: colors.gradients.primary,
          '&:hover': {
            background: colors.gradients.accent,
            boxShadow: colors.shadows.purple,
          },
        },
        containedSecondary: {
          background: colors.gradients.secondary,
          border: `1px solid ${colors.purple[200]}`,
          '&:hover': {
            background: colors.neutral[100],
            boxShadow: colors.shadows.lg,
          },
        },
        outlined: {
          borderWidth: '2px',
          borderColor: colors.purple[300],
          color: colors.purple[700],
          '&:hover': {
            borderWidth: '2px',
            backgroundColor: `${colors.purple[500]}${colors.opacity[10]}`,
            borderColor: colors.purple[500],
          },
        },
        outlinedPrimary: {
          borderColor: colors.primary,
          color: colors.primary,
          '&:hover': {
            backgroundColor: `${colors.primary}${colors.opacity[10]}`,
            borderColor: colors.primary,
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          backgroundColor: colors.white,
          borderRadius: 16,
        },
        rounded: {
          borderRadius: 16,
        },
        elevation1: {
          boxShadow: colors.shadows.sm,
        },
        elevation2: {
          boxShadow: colors.shadows.md,
        },
        elevation3: {
          boxShadow: colors.shadows.lg,
        },
        elevation4: {
          boxShadow: colors.shadows.xl,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 20,
          boxShadow: colors.shadows.sm,
          border: `1px solid ${colors.neutral[200]}`,
          '&:hover': {
            boxShadow: colors.shadows.md,
            transform: 'translateY(-2px)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: `1px solid ${colors.neutral[200]}`,
          padding: '16px',
        },
        head: {
          fontWeight: 700,
          backgroundColor: colors.neutral[50],
          color: colors.text.primary,
          fontSize: '0.875rem',
          textTransform: 'uppercase',
          letterSpacing: '0.5px',
          borderBottom: `2px solid ${colors.purple[300]}`,
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 12,
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: colors.purple[400],
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: colors.primary,
              borderWidth: '2px',
            },
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: colors.neutral[300],
            },
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 20,
          fontWeight: 600,
          height: 32,
        },
        colorPrimary: {
          backgroundColor: `${colors.primary}${colors.opacity[15]}`,
          color: colors.primary,
        },
        colorSecondary: {
          backgroundColor: `${colors.secondary}`,
          color: colors.text.primary,
          border: `1px solid ${colors.neutral[300]}`,
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: colors.white,
          color: colors.text.primary,
          boxShadow: colors.shadows.sm,
          borderBottom: `1px solid ${colors.neutral[200]}`,
        },
      },
    },
    MuiToolbar: {
      styleOverrides: {
        root: {
          minHeight: 64,
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          '&.Mui-selected': {
            backgroundColor: `${colors.primary}${colors.opacity[15]}`,
            color: colors.primary,
            '&:hover': {
              backgroundColor: `${colors.primary}${colors.opacity[20]}`,
            },
          },
          '&:hover': {
            backgroundColor: `${colors.neutral[100]}`,
          },
        },
      },
    },
  },
});

export default theme;